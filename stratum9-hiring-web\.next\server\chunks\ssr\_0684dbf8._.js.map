{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n\n// Dynamic uploading messages for job generation\nexport const JOB_GENERATION_UPLOAD_MESSAGES = [\n  \"Analyzing your job description...\",\n  \"Extracting key requirements...\",\n  \"Processing document content...\",\n  \"Identifying skills and qualifications...\",\n  \"Parsing job details...\",\n  \"Almost ready...\",\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB;AAGxB,MAAM,iCAAiC;IAC5C;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/internal-hiring.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 327, height: 221, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAAsElEQVR42gGlAFr/ACQbEEtSOSuSBwUDCQgEAwcLBgUKDAoMFFM/RIoTGCM0AJRvKLu8lVvfWkAeZSESDB8RDQ4XKzpZf1Nom+01U4fBAJ11K+2xiVD4WFA/pxYfLUEfJzVAT2B8nFJ1svlOZ5HrAE0/IbxXTj3pUlNU9hsdIU1cW1lg3NjR8K6sp9ptZVWzACceCzM8MyqVT09Q4hYXF0cnJiQnwry0ykQ+NUcmHQsrlMw2RABhsvoAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 5 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkV,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/images/external-hiring.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 363, height: 251, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAA0UlEQVR42gHGADn/AAcICwJPYH0ci6bWOn+JrmtdeKcvIzBHEgIDBAEAAAAAADA9UxGVt+8ynK3XXaeGiNKEnM9WZ4nCNSApOREBAQICAEhdgh2Tuvw1hZ/Tk7W3x/i4w9q8o7fdgiw7VRkBAgQDABoiMAlri8Ixgp7UrtXX4fji5On1xM3ftSIrOxMAAQEBAAECBAI2R2MVfZ/YbWFykPSPpMiWkq/fTSAqOQ8BAQIBAAECAwFJYowefJnKcjw7PvZodpGdjrDqNDI/VhQAAAAAEXJLIcmfNMIAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8X,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/jobRequirement/HiringType.tsx"], "sourcesContent": ["\"use client\";\n// Internal libraries\nimport { useState } from \"react\";\n\n// External libraries\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { useRouter } from \"next/navigation\";\nimport { useTranslations } from \"next-intl\";\n// Components\nimport Button from \"@/components/formElements/Button\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\n\n// Constants\nimport ROUTES from \"@/constants/routes\";\nimport { ACTIVE } from \"@/constants/commonConstants\";\nimport { HIRING_TYPE } from \"@/constants/jobRequirementConstant\";\n\n// Assets\nimport internalHiring from \"../../../../public/assets/images/internal-hiring.png\";\nimport externalHiring from \"../../../../public/assets/images/external-hiring.png\";\n\n// CSS\nimport style from \"@/styles/commonPage.module.scss\";\n\nfunction HiringType() {\n  const router = useRouter();\n  const [hiringType, setHiringType] = useState(HIRING_TYPE.INTERNAL);\n\n  const t = useTranslations(\"jobRequirement\");\n  const tCommon = useTranslations(\"common\");\n\n  return (\n    <div className={style.job_page}>\n      <div className=\"container\">\n        <div className=\"common-page-header\">\n          <div className=\"breadcrumb\">\n            <Link href={ROUTES.DASHBOARD}>{tCommon(\"home\")} </Link>\n            <Link href=\"#\">{tCommon(\"job_requirement_generations\")}</Link>\n          </div>\n          <div className=\"common-page-head-section\">\n            <div className=\"main-heading\">\n              <h2>\n                <Button className=\"clear-btn p-0 m-0\">\n                  <BackArrowIcon\n                    onClick={() => {\n                      router.push(ROUTES.DASHBOARD);\n                    }}\n                  />\n                </Button>{\" \"}\n                {t(\"select_hiring_type\")}\n              </h2>\n            </div>\n            <p className=\"description\">{t(\"select_the_type_of_hiring_below_to_proceed_with_the_job_description_creation\")}</p>\n          </div>\n        </div>\n        <div className=\"row g-5\" style={{ minHeight: window.innerHeight - 430 }}>\n          <div className=\"col-md-6\">\n            <div className={`hiring-card ${hiringType === HIRING_TYPE.INTERNAL && ACTIVE}`}>\n              <div className=\"hiring-content\">\n                <div className=\"radio-wrapper\">\n                  <input\n                    className=\"radio-input\"\n                    type=\"radio\"\n                    name=\"hiringType\"\n                    id=\"internalHiring\"\n                    checked={hiringType === HIRING_TYPE.INTERNAL}\n                    onChange={() => setHiringType(HIRING_TYPE.INTERNAL)}\n                  />\n                  <label className=\"radio-label\" htmlFor=\"internalHiring\">\n                    {t(\"internal_hiring\")}\n                  </label>\n                </div>\n                <p>{t(\"craft_job_descriptions_for_internal_openings_and_promote_opportunities_within_your_organization\")}</p>\n              </div>\n              <div className=\"hiring-image\">\n                <Image src={internalHiring} alt=\"internal-hiring\" className=\"hiring-img\" />\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className={`hiring-card ${hiringType === HIRING_TYPE.EXTERNAL ? ACTIVE : \"\"}`}>\n              <div className=\"hiring-content\">\n                <div className=\"radio-wrapper\">\n                  <input\n                    className=\"radio-input\"\n                    type=\"radio\"\n                    name=\"hiringType\"\n                    id=\"externalHiring\"\n                    checked={hiringType === HIRING_TYPE.EXTERNAL}\n                    onChange={() => setHiringType(HIRING_TYPE.EXTERNAL)}\n                  />\n                  <label className=\"radio-label\" htmlFor=\"externalHiring\">\n                    {t(\"external_hiring\")}\n                  </label>\n                </div>\n                <p>{t(\"attract_top_talent_by_creating_compelling_job_descriptions_for_external_candidates\")}</p>\n              </div>\n              <div className=\"hiring-image\">\n                <Image src={externalHiring} alt=\"internal-hiring\" className=\"hiring-img\" />\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"button-align py-5\">\n          <Button\n            className=\"primary-btn rounded-md\"\n            onClick={() => {\n              router.push(`${ROUTES.JOBS.GENERATE_JOB}?hiringType=${hiringType}`);\n            }}\n            disabled={!hiringType}\n          >\n            {t(\"continue\")}\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default HiringType;\n"], "names": [], "mappings": ";;;;AACA,qBAAqB;AACrB;AAEA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AAEA,YAAY;AACZ;AACA;AACA;AAEA,SAAS;AACT;AACA;AAEA,MAAM;AACN;AAvBA;;;;;;;;;;;;;;;AAyBA,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,0IAAA,CAAA,cAAW,CAAC,QAAQ;IAEjE,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,8OAAC;QAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,QAAQ;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,0HAAA,CAAA,UAAM,CAAC,SAAS;;wCAAG,QAAQ;wCAAQ;;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAK,QAAQ;;;;;;;;;;;;sCAE1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;0DACC,8OAAC,4IAAA,CAAA,UAAM;gDAAC,WAAU;0DAChB,cAAA,8OAAC,oJAAA,CAAA,UAAa;oDACZ,SAAS;wDACP,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS;oDAC9B;;;;;;;;;;;4CAEM;4CACT,EAAE;;;;;;;;;;;;8CAGP,8OAAC;oCAAE,WAAU;8CAAe,EAAE;;;;;;;;;;;;;;;;;;8BAGlC,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,WAAW,OAAO,WAAW,GAAG;oBAAI;;sCACpE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAW,CAAC,YAAY,EAAE,eAAe,0IAAA,CAAA,cAAW,CAAC,QAAQ,IAAI,mIAAA,CAAA,SAAM,EAAE;;kDAC5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAU;wDACV,MAAK;wDACL,MAAK;wDACL,IAAG;wDACH,SAAS,eAAe,0IAAA,CAAA,cAAW,CAAC,QAAQ;wDAC5C,UAAU,IAAM,cAAc,0IAAA,CAAA,cAAW,CAAC,QAAQ;;;;;;kEAEpD,8OAAC;wDAAM,WAAU;wDAAc,SAAQ;kEACpC,EAAE;;;;;;;;;;;;0DAGP,8OAAC;0DAAG,EAAE;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAK,gUAAA,CAAA,UAAc;4CAAE,KAAI;4CAAkB,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAIlE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAW,CAAC,YAAY,EAAE,eAAe,0IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,mIAAA,CAAA,SAAM,GAAG,IAAI;;kDAChF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAU;wDACV,MAAK;wDACL,MAAK;wDACL,IAAG;wDACH,SAAS,eAAe,0IAAA,CAAA,cAAW,CAAC,QAAQ;wDAC5C,UAAU,IAAM,cAAc,0IAAA,CAAA,cAAW,CAAC,QAAQ;;;;;;kEAEpD,8OAAC;wDAAM,WAAU;wDAAc,SAAQ;kEACpC,EAAE;;;;;;;;;;;;0DAGP,8OAAC;0DAAG,EAAE;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAK,gUAAA,CAAA,UAAc;4CAAE,KAAI;4CAAkB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKpE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;wBACL,WAAU;wBACV,SAAS;4BACP,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY;wBACpE;wBACA,UAAU,CAAC;kCAEV,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf;uCAEe", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}