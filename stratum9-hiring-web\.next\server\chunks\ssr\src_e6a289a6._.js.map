{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/PreviewResumeIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction PreviewResumeIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"24\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        d=\"M19.4419 1.28516V1.28613C19.546 1.28464 19.6494 1.30297 19.7456 1.34277L19.8169 1.37695C19.8854 1.41401 19.948 1.46142 20.0024 1.51758L27.5933 9.10742V9.1084L27.645 9.16602C27.7603 9.30714 27.8241 9.48419 27.8247 9.66797V27.8438C27.8247 28.605 27.5222 29.3347 26.9839 29.873C26.4456 30.4114 25.7159 30.7138 24.9546 30.7139H7.04346C6.37733 30.7139 5.73491 30.483 5.22412 30.0645L5.01416 29.873C4.47584 29.3347 4.17334 28.6051 4.17334 27.8438V4.15527C4.17338 3.39401 4.47586 2.66428 5.01416 2.12598L5.22412 1.93457C5.7349 1.5161 6.37736 1.28517 7.04346 1.28516H19.4419ZM7.04541 2.87012C6.7472 2.87016 6.45962 2.97377 6.23096 3.16113L6.13721 3.24707C5.89621 3.48807 5.76029 3.81446 5.76025 4.15527V27.8438C5.76026 28.1846 5.89617 28.5119 6.13721 28.7529L6.23096 28.8379C6.45963 29.0253 6.74717 29.1289 7.04541 29.1289H24.9546L25.0806 29.123C25.2063 29.1109 25.3296 29.0796 25.4468 29.0312C25.6029 28.9668 25.7452 28.8723 25.8647 28.7529C25.9842 28.6336 26.0794 28.4919 26.144 28.3359L26.186 28.2168C26.2227 28.096 26.2417 27.9704 26.2417 27.8438V10.4609H21.1753C20.5059 10.4609 19.8635 10.195 19.3901 9.72168C18.9168 9.2483 18.6509 8.60595 18.6509 7.93652V2.87012H7.04541ZM20.2349 7.93652C20.2349 8.18554 20.3332 8.4245 20.5093 8.60059L20.5786 8.66309C20.7456 8.79983 20.9556 8.87597 21.1733 8.87598H25.1196L20.2349 3.99121V7.93652Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M12.3069 6.4873C12.7821 6.33047 13.2913 6.29936 13.7844 6.39746L13.9934 6.44727C14.4755 6.58088 14.9162 6.83691 15.2717 7.19238L15.4182 7.34961C15.7449 7.72854 15.9685 8.18664 16.0667 8.67969L16.1008 8.8916C16.162 9.38833 16.0941 9.89378 15.9016 10.3584C15.7093 10.8228 15.4003 11.2282 15.0061 11.5361L14.8313 11.6621C14.3537 11.9812 13.7924 12.1513 13.218 12.1514H13.217C12.5433 12.1504 11.8938 11.9165 11.3772 11.4932L11.1643 11.2998C10.6198 10.7553 10.3138 10.0171 10.3127 9.24707L10.3206 9.03223C10.3575 8.5332 10.5227 8.05091 10.802 7.63281L10.928 7.45801C11.2359 7.06383 11.6414 6.75485 12.1057 6.5625L12.3069 6.4873ZM13.0872 7.93359C12.7852 7.964 12.5009 8.097 12.2844 8.31348C12.037 8.56093 11.8982 8.89713 11.8977 9.24707L11.9124 9.44043C11.9409 9.63256 12.0116 9.81665 12.1204 9.97949L12.2395 10.1338C12.3699 10.2775 12.5314 10.3909 12.7122 10.4658L12.8977 10.5273C13.0859 10.5743 13.283 10.5791 13.4749 10.541L13.6624 10.4893C13.8453 10.4238 14.0121 10.3182 14.1506 10.1797C14.3352 9.99509 14.461 9.75994 14.512 9.50391L14.5364 9.31055C14.5426 9.18148 14.5296 9.05212 14.4983 8.92676L14.4368 8.74121C14.3618 8.56046 14.2485 8.39894 14.1047 8.26855L13.9504 8.14941C13.7335 8.00453 13.4788 7.92685 13.218 7.92676L13.0872 7.93359Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M14.0181 12.252L14.2183 12.2568C15.2193 12.3076 16.1692 12.7274 16.8813 13.4395L17.02 13.585C17.693 14.328 18.0686 15.2965 18.0698 16.3037V16.7266C18.0698 16.9104 18.0056 17.0875 17.8901 17.2285L17.8374 17.2871C17.6888 17.4356 17.487 17.5186 17.2769 17.5186C17.0931 17.5185 16.9158 17.4552 16.7749 17.3398L16.7163 17.2871C16.5677 17.1385 16.4849 16.9367 16.4849 16.7266V16.3037L16.4722 16.0605C16.4236 15.5765 16.2328 15.1174 15.9243 14.7412L15.7612 14.5605C15.3566 14.1559 14.8257 13.9064 14.2612 13.8496L14.0171 13.8369H12.4175C11.8453 13.8377 11.2937 14.0369 10.855 14.3965L10.6743 14.5605C10.2119 15.023 9.95162 15.6497 9.95068 16.3037V16.7266C9.95062 16.9104 9.8865 17.0875 9.771 17.2285L9.71826 17.2871C9.56964 17.4356 9.36781 17.5186 9.15771 17.5186C8.97399 17.5185 8.7967 17.4552 8.65576 17.3398L8.59717 17.2871C8.44859 17.1385 8.3658 16.9367 8.36572 16.7266V16.3037L8.37061 16.1025C8.42148 15.1015 8.84113 14.1515 9.55322 13.4395L9.69873 13.3018C10.4418 12.6286 11.4101 12.2532 12.4175 12.252H14.0181Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M17.1772 21.002L17.2554 21.0059C17.4369 21.0238 17.6077 21.1034 17.7378 21.2334C17.8864 21.382 17.9701 21.5838 17.9702 21.7939C17.9702 22.0041 17.8864 22.2058 17.7378 22.3545C17.6076 22.4847 17.437 22.5651 17.2554 22.583L17.1772 22.5869H8.77197C8.58813 22.5869 8.411 22.5227 8.27002 22.4072L8.21143 22.3545C8.06294 22.2059 7.97998 22.004 7.97998 21.7939C7.98005 21.5838 8.06284 21.382 8.21143 21.2334L8.27002 21.1807C8.41096 21.0653 8.58824 21.002 8.77197 21.002H17.1772Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n      <path\n        d=\"M21.9321 24.6504C22.1422 24.6504 22.3441 24.7334 22.4927 24.8818L22.5454 24.9404C22.6609 25.0814 22.725 25.2585 22.7251 25.4424C22.7251 25.6526 22.6413 25.8543 22.4927 26.0029C22.344 26.1516 22.1423 26.2354 21.9321 26.2354H8.77197C8.58813 26.2353 8.411 26.1712 8.27002 26.0557L8.21143 26.0029C8.06294 25.8543 7.97998 25.6525 7.97998 25.4424C7.98005 25.2323 8.06284 25.0304 8.21143 24.8818L8.27002 24.8291C8.41096 24.7137 8.58824 24.6505 8.77197 24.6504H21.9321Z\"\n        stroke=\"#436EB6\"\n        strokeWidth=\"0.2\"\n      />\n    </svg>\n  );\n}\n\nexport default PreviewResumeIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,kBAAkB,EAAE,SAAS,EAA0B;IAC9D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;;;;;;;AAIpB;uCAEe", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/conductInterview.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"additional\": \"conductInterview-module-scss-module__yztraq__additional\",\n  \"completed\": \"conductInterview-module-scss-module__yztraq__completed\",\n  \"conduct_interview\": \"conductInterview-module-scss-module__yztraq__conduct_interview\",\n  \"conduct_interview_page\": \"conductInterview-module-scss-module__yztraq__conduct_interview_page\",\n  \"current\": \"conductInterview-module-scss-module__yztraq__current\",\n  \"question_info_box\": \"conductInterview-module-scss-module__yztraq__question_info_box\",\n  \"summary_card_height\": \"conductInterview-module-scss-module__yztraq__summary_card_height\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/CandidatesServices/candidatesApplicationServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport {\n  AdditionalInfoPayload,\n  CandidateApplication,\n  CandidateProfileResponse,\n  PromoteDemotePayload,\n  topCandidateApplication,\n} from \"@/interfaces/candidatesInterface\";\n\n/**\n * Fetch candidates with their job applications.\n *\n * Automatically sends query parameters to backend, including:\n * - jobId: Filter by job\n * - searchStr: For candidate name search\n * - isActive: true for active, false for archived\n * - page: For pagination (used as offset)\n * - limit: Number of items per page\n */\nexport const fetchCandidatesApplications = (data: {\n  page?: number; // Offset for pagination\n  limit?: number; // Max results per page\n  searchStr?: string; // Search candidates by name\n  isActive?: boolean; // Filter: true = active, false = archived\n  jobId?: number; // Optional jobId filter\n}): Promise<ApiResponse<CandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, { ...data });\n};\n\nexport const fetchTopCandidatesApplications = (jobId?: number): Promise<ApiResponse<topCandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {\n    jobId, // Optional jobId filter\n  });\n};\n\nexport const promoteDemoteCandidate = async (payload: PromoteDemotePayload): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);\n};\n\nexport const addApplicantAdditionalInfo = async (payload: AdditionalInfoPayload): Promise<ApiResponse<null>> => {\n  return await http.post(endpoint.candidatesApplication.ADDITIONAL_INFO, payload);\n};\n\n/**\n * Fetches candidate profile details by candidate ID\n * @param candidateId - The ID of the candidate\n */\nexport const fetchCandidateProfile = (candidateId: number | string): Promise<ApiResponse<CandidateProfileResponse>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATE_DETAILS, { candidateId });\n};\n\n/**\n * Updates the job application status (Hire/Reject)\n * @param jobApplicationId - The ID of the job application to update\n * @param payload - The status payload (\"Final-Hired\" or \"Rejected\")\n */\nexport const updateJobApplicationStatus = async (jobApplicationId: number, status: string): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(\":jobApplicationId\", jobApplicationId.toString()), {\n    status,\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAoBO,MAAM,8BAA8B,CAAC;IAO1C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,gCAAgC,EAAE;QAAE,GAAG,IAAI;IAAC;AAC7F;AAEO,MAAM,iCAAiC,CAAC;IAC7C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,oCAAoC,EAAE;QACnF;IACF;AACF;AAEO,MAAM,yBAAyB,OAAO;IAC3C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,wBAAwB,EAAE;AACjF;AAEO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,eAAe,EAAE;AACzE;AAMO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,qBAAqB,EAAE;QAAE;IAAY;AACtF;AAOO,MAAM,6BAA6B,OAAO,kBAA0B;IACzE,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB,iBAAiB,QAAQ,KAAK;QAC5I;IACF;AACF", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/AiMarkIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction AiMarkIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"25\" viewBox=\"0 0 32 32\" fill=\"none\">\n      <path\n        d=\"M13.294 7.43666L14.097 9.66666C14.989 12.1417 16.938 14.0907 19.413 14.9827L21.643 15.7857C21.844 15.8587 21.844 16.1437 21.643 16.2157L19.413 17.0187C16.938 17.9107 14.989 19.8597 14.097 22.3347L13.294 24.5647C13.221 24.7657 12.936 24.7657 12.864 24.5647L12.061 22.3347C11.169 19.8597 9.22001 17.9107 6.74501 17.0187L4.51501 16.2157C4.31401 16.1427 4.31401 15.8577 4.51501 15.7857L6.74501 14.9827C9.22001 14.0907 11.169 12.1417 12.061 9.66666L12.864 7.43666C12.936 7.23466 13.221 7.23466 13.294 7.43666Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M23.3321 2.07725L23.7391 3.20625C24.1911 4.45925 25.1781 5.44625 26.4311 5.89825L27.5601 6.30525C27.6621 6.34225 27.6621 6.48625 27.5601 6.52325L26.4311 6.93025C25.1781 7.38225 24.1911 8.36925 23.7391 9.62225L23.3321 10.7513C23.2951 10.8533 23.1511 10.8533 23.1141 10.7513L22.7071 9.62225C22.2551 8.36925 21.2681 7.38225 20.0151 6.93025L18.8861 6.52325C18.7841 6.48625 18.7841 6.34225 18.8861 6.30525L20.0151 5.89825C21.2681 5.44625 22.2551 4.45925 22.7071 3.20625L23.1141 2.07725C23.1511 1.97425 23.2961 1.97425 23.3321 2.07725Z\"\n        fill=\"black\"\n      />\n      <path\n        d=\"M23.3321 21.2484L23.7391 22.3774C24.1911 23.6304 25.1781 24.6174 26.4311 25.0694L27.5601 25.4764C27.6621 25.5134 27.6621 25.6574 27.5601 25.6944L26.4311 26.1014C25.1781 26.5534 24.1911 27.5404 23.7391 28.7934L23.3321 29.9224C23.2951 30.0244 23.1511 30.0244 23.1141 29.9224L22.7071 28.7934C22.2551 27.5404 21.2681 26.5534 20.0151 26.1014L18.8861 25.6944C18.7841 25.6574 18.7841 25.5134 18.8861 25.4764L20.0151 25.0694C21.2681 24.6174 22.2551 23.6304 22.7071 22.3774L23.1141 21.2484C23.1511 21.1464 23.2961 21.1464 23.3321 21.2484Z\"\n        fill=\"black\"\n      />\n    </svg>\n  );\n}\n\nexport default AiMarkIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/AIVerifiedIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction AIVerifiedIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"150\" height=\"150\" viewBox=\"0 0 196 196\" fill=\"none\">\n      <g clip-path=\"url(#clip0_13085_4945)\">\n        <path\n          d=\"M84.1489 44.693C65.223 49.5916 50.4346 64.1256 45.0945 82.8875L136.833 59.1387C126.378 48.6518 112.34 42.9375 97.907 42.9375C93.328 42.9375 88.709 43.5115 84.1489 44.693Z\"\n          fill=\"#436EB6\"\n        />\n        <path\n          d=\"M111.678 151.027C131.441 145.911 146.439 130.616 151.26 110.852L57.5745 135.105C71.3789 150.047 91.9152 156.142 111.678 151.027Z\"\n          fill=\"#436EB6\"\n        />\n        <path\n          d=\"M189.667 81.8553L193.432 75.8991L187.438 72.1947L190.55 65.8727L184.196 62.8263L186.618 56.2101L179.977 53.8554L181.682 47.0183L174.828 45.3847L175.797 38.4047L168.809 37.5091L169.03 30.466L161.984 30.3168L161.455 23.2905L154.435 23.8918L153.163 16.9623L146.246 18.3057L144.244 11.5506L137.51 13.6215L134.802 7.11666L128.327 9.89184L124.942 3.71286L118.797 7.16081L114.775 1.37708L109.031 5.45786L104.412 0.134554L99.135 4.80401L93.9799 0L89.2284 5.20347L83.5919 0.975519L79.4207 6.65413L73.3678 3.0485L69.8253 9.13918L63.4234 6.19791L60.5473 12.6313L53.868 10.3859L51.692 17.0884L44.8129 15.5663L43.3622 22.4622L36.3591 21.6801L35.6485 28.6895L28.6033 28.6559L28.6411 35.701L21.6317 36.418L22.4201 43.419L15.5263 44.876L17.0548 51.7551L10.3544 53.9353L12.604 60.6125L6.17478 63.4928L9.12236 69.8926L3.03588 73.4414L6.64572 79.4922L0.969212 83.6675L5.20136 89.2999L0 94.0535L4.80821 99.2044L0.142964 104.486L5.47047 109.096L1.3939 114.842L7.18184 118.86L3.73809 125.007L9.92128 128.388L7.1503 134.865L13.6573 137.569L11.5906 144.305L18.3477 146.303L17.0085 153.22L23.9402 154.487L23.3452 161.507L30.3714 162.031L30.5249 169.076L37.568 168.849L38.4699 175.837L45.4478 174.864L47.0877 181.716L53.9226 180.006L56.2815 186.646L62.8957 184.22L65.9463 190.571L72.2662 187.455L75.9748 193.445L81.9268 189.675L86.2514 195.238L91.7702 190.857L96.6626 195.928L101.683 190.985L107.086 195.507L111.554 190.058L117.407 193.981L121.269 188.088L127.507 191.366L130.722 185.096L137.271 187.693L139.8 181.116L146.589 183.002L148.405 176.195L155.356 177.349L156.438 170.386L163.473 170.794L163.809 163.755L170.846 163.412L170.432 156.377L177.393 155.29L176.235 148.34L183.04 146.519L181.148 139.733L187.722 137.197L185.119 130.648L191.387 127.429L188.103 121.196L193.992 117.329L190.064 111.478L195.51 107.006L190.983 101.607L195.922 96.5827L190.846 91.6946L195.224 86.1736L189.658 81.8532L189.667 81.8553ZM119.366 180.73C73.6727 192.56 26.8751 165.008 15.047 119.316C3.21669 73.6201 30.7667 26.8226 76.4605 14.9944C122.154 3.16413 168.952 30.7162 180.782 76.41C192.61 122.104 165.06 168.901 119.366 180.73Z\"\n          fill=\"#436EB6\"\n        />\n        <path d=\"M167.926 110.497L167.228 113.896L165.458 113.532L166.156 110.133L167.926 110.497Z\" fill=\"#436EB6\" />\n        <path d=\"M45.6875 146.125L43.3979 143.516L44.7561 142.324L47.0456 144.931L45.6875 146.123V146.125Z\" fill=\"#436EB6\" />\n        <path d=\"M28.0042 85.4308L28.7022 82.0312L30.4745 82.395L29.7765 85.7946L28.0063 85.4308H28.0042Z\" fill=\"#436EB6\" />\n        <path d=\"M150.24 49.8047L152.532 52.4138L151.174 53.6058L148.882 50.9989L150.24 49.8068V49.8047Z\" fill=\"#436EB6\" />\n        <path\n          d=\"M25.7139 109.445C25.2033 107.54 25.2725 105.96 25.9212 104.705C26.564 103.428 27.7819 102.549 29.5752 102.068C31.3684 101.588 32.8627 101.74 34.058 102.525C35.2473 103.287 36.0973 104.621 36.6078 106.526L36.806 107.266L33.3092 108.203L33.0479 107.228C32.8317 106.421 32.5186 105.88 32.1087 105.606C31.6928 105.309 31.1822 105.241 30.577 105.404C29.9718 105.566 29.5633 105.879 29.3516 106.344C29.1339 106.787 29.1331 107.412 29.3494 108.219C29.6136 109.205 30.1026 109.999 30.8164 110.601C31.5526 111.196 32.5736 111.824 33.8793 112.483C34.9698 113.056 35.8681 113.584 36.5742 114.067C37.2743 114.528 37.9321 115.145 38.5474 115.917C39.1627 116.689 39.6205 117.635 39.9208 118.756C40.4313 120.662 40.354 122.256 39.6888 123.539C39.0177 124.8 37.7855 125.671 35.9922 126.151C34.199 126.632 32.6965 126.494 31.4848 125.738C30.267 124.959 29.4029 123.617 28.8924 121.711L28.505 120.265L32.0018 119.329L32.4523 121.01C32.8607 122.534 33.7037 123.125 34.9814 122.783C36.2591 122.44 36.6937 121.507 36.2853 119.983C36.021 118.996 35.5208 118.205 34.7846 117.61C34.0709 117.008 33.0611 116.378 31.7553 115.719C30.6649 115.146 29.7696 114.629 29.0695 114.168C28.3633 113.685 27.7026 113.057 27.0873 112.285C26.472 111.513 26.0142 110.566 25.7139 109.445ZM52.8461 115.077C53.9392 119.156 52.6029 121.701 48.8371 122.71C47.0438 123.19 45.5414 123.052 44.3296 122.296C43.1119 121.517 42.2478 120.175 41.7372 118.27L41.5751 117.664L45.0719 116.727L45.2971 117.568C45.7115 119.115 46.5576 119.717 47.8353 119.374C48.5302 119.188 48.9887 118.837 49.2108 118.321C49.4329 117.805 49.3998 117.009 49.1115 115.933L47.9583 111.629C47.6806 113.193 46.7236 114.195 45.0873 114.633C43.7424 114.993 42.5994 114.807 41.6582 114.074C40.7171 113.341 40.0123 112.101 39.5438 110.352L38.3906 106.049C37.8861 104.166 37.9776 102.58 38.6652 101.29C39.3528 100.001 40.6044 99.113 42.42 98.6265C44.2357 98.14 45.7636 98.2831 47.0037 99.056C48.2439 99.8288 49.1162 101.157 49.6207 103.04L52.8461 115.077ZM45.6994 110.865C46.9771 110.523 47.4087 109.578 46.9943 108.032L45.8591 103.795C45.4507 102.271 44.6077 101.68 43.33 102.022C42.0523 102.365 41.6177 103.298 42.0261 104.822L43.1613 109.059C43.5757 110.605 44.4217 111.208 45.6994 110.865ZM59.4973 94.3389L65.8038 117.875L62.1053 118.866L55.7987 95.3299L59.4973 94.3389ZM71.8032 116.268L68.4745 117.16L62.168 93.6233L66.808 92.38L74.3823 105.45L70.6074 91.362L73.9025 90.4791L80.209 114.015L76.4096 115.033L67.2355 99.2207L71.8032 116.268ZM86.1522 112.423L82.8235 113.315L76.5169 89.7785L81.1569 88.5352L88.7313 101.605L84.9564 87.5172L88.2514 86.6343L94.558 110.171L90.7585 111.189L81.5844 95.3759L86.1522 112.423ZM102.333 93.4921L103.234 96.8544L98.1568 98.2149L100.004 105.108L106.392 103.396L107.293 106.758L97.206 109.461L90.8995 85.9247L100.986 83.2219L101.887 86.5842L95.499 88.296L97.2558 94.8525L102.333 93.4921ZM117.318 104.072C117.082 103.727 116.896 103.392 116.761 103.068C116.626 102.744 116.441 102.145 116.207 101.27L115.216 97.5719C114.928 96.4959 114.533 95.7848 114.032 95.4386C113.525 95.07 112.845 94.9998 111.993 95.2281L110.716 95.5704L113.283 105.153L109.585 106.144L103.278 82.6078L108.86 81.1123C110.765 80.6017 112.275 80.6777 113.389 81.34C114.498 81.98 115.298 83.219 115.791 85.0571L116.286 86.9064C116.935 89.3272 116.555 91.135 115.145 92.3295C116.115 92.4539 116.905 92.891 117.514 93.6407C118.118 94.3679 118.593 95.3816 118.942 96.6817L119.915 100.313C120.095 100.985 120.265 101.529 120.423 101.943C120.576 102.334 120.797 102.707 121.084 103.063L117.318 104.072ZM109.815 92.2081L111.261 91.8207C112 91.6225 112.498 91.2848 112.755 90.8076C113.034 90.3245 113.053 89.6346 112.813 88.738L112.191 86.418C111.963 85.5662 111.641 84.9918 111.225 84.6948C110.832 84.3918 110.31 84.3274 109.66 84.5016L107.878 84.9791L109.815 92.2081ZM125.147 76.7482L128.543 75.8383L131.218 100.348L125.704 101.825L115.766 79.2618L119.498 78.2618L127.467 96.7039L125.147 76.7482ZM133.967 74.3847L140.274 97.9209L136.575 98.912L130.269 75.3757L133.967 74.3847ZM148.105 81.2275L149.006 84.5898L143.929 85.9502L145.776 92.843L152.164 91.1312L153.065 94.4935L142.978 97.1963L136.672 73.6601L146.759 70.9573L147.66 74.3196L141.271 76.0314L143.028 82.5879L148.105 81.2275ZM162.216 92.0415L157.139 93.4019L148.21 70.5684L151.808 69.6044L158.765 87.6329L155.573 68.5954L159.138 67.6404L165.996 85.8394L162.903 66.6314L166.131 65.7665L169.815 90.0054L164.906 91.3208L160.201 79.1397L162.216 92.0415Z\"\n          fill=\"#436EB6\"\n        />\n        <path\n          d=\"M51.0894 57.3542L49.0756 56.0873L47.4451 57.984L48.9999 59.7849L47.78 61.2039L39.2978 51.1756L41.2424 48.9136L52.4301 55.7947L51.0894 57.3542ZM47.5597 55.1506L41.9459 51.6196L46.2794 56.6399L47.5597 55.1506ZM47.3313 42.4979L56.0634 52.0874L54.5565 53.4596L45.8244 43.8701L47.3313 42.4979ZM60.347 33.0923L61.9587 32.1414L66.8254 44.3287L64.2083 45.8726L55.8947 35.7189L57.6661 34.6739L64.3822 42.995L60.347 33.0923ZM72.9924 33.0346L73.6868 34.7524L71.093 35.8009L72.5165 39.3223L75.7802 38.003L76.4746 39.7207L71.3213 41.8039L66.4607 29.7796L71.6139 27.6965L72.3083 29.4142L69.0446 30.7335L70.3986 34.0831L72.9924 33.0346ZM83.8828 37.5586C83.7629 37.371 83.6699 37.1897 83.6038 37.0149C83.5376 36.8401 83.4498 36.5181 83.3403 36.049L82.8772 34.0642C82.7425 33.4869 82.5448 33.1017 82.2842 32.9089C82.0207 32.704 81.6605 32.6549 81.2034 32.7615L80.5177 32.9215L81.7176 38.0638L79.7329 38.5269L76.7858 25.8967L79.7809 25.1978C80.8034 24.9592 81.6053 25.0258 82.1867 25.3975C82.7653 25.7571 83.1697 26.4302 83.3998 27.4165L83.6314 28.4089C83.9345 29.708 83.7008 30.6631 82.9301 31.2742C83.4441 31.3572 83.8567 31.6033 84.1679 32.0127C84.4762 32.4101 84.7118 32.9576 84.8746 33.6552L85.3293 35.6039C85.4135 35.9648 85.4943 36.2567 85.5717 36.4796C85.6462 36.6905 85.7569 36.893 85.9037 37.0871L83.8828 37.5586ZM80.0967 31.1172L80.8726 30.9362C81.2695 30.8435 81.5404 30.6725 81.6851 30.4231C81.8419 30.1709 81.8641 29.8042 81.7519 29.3231L81.4614 28.0781C81.3547 27.621 81.1933 27.3099 80.9772 27.1447C80.7731 26.9767 80.4967 26.9334 80.1478 27.0148L79.1915 27.2379L80.0967 31.1172ZM89.7425 23.3006L91.1863 36.1895L89.1609 36.4164L87.7171 23.5275L89.7425 23.3006ZM99.6167 28.7035L99.599 30.5562L96.9682 30.5311L96.917 35.8854L94.8791 35.8659L95.003 22.897L100.394 22.9485L100.377 24.8012L97.0233 24.7692L96.9859 28.6784L99.6167 28.7035ZM107.046 23.4013L105.482 36.2761L103.459 36.0303L105.023 23.1554L107.046 23.4013ZM115.633 30.6744L115.192 32.4739L112.474 31.8076L111.57 35.4966L114.989 36.3349L114.548 38.1344L109.149 36.8107L112.238 24.2143L117.636 25.538L117.195 27.3375L113.776 26.4991L112.916 30.0081L115.633 30.6744ZM125.84 28.3285C126.776 28.7169 127.373 29.2592 127.632 29.9553C127.892 30.6515 127.82 31.4844 127.417 32.454L124.902 38.5114C124.499 39.4811 123.96 40.1197 123.284 40.4275C122.608 40.7352 121.802 40.6948 120.867 40.3064L117.889 39.0701L122.863 27.0922L125.84 28.3285ZM120.482 38.1405L121.543 38.5811C121.851 38.709 122.123 38.7148 122.358 38.5986C122.605 38.4871 122.814 38.2261 122.984 37.8154L125.571 31.5869C125.741 31.1762 125.779 30.8441 125.683 30.5905C125.599 30.3417 125.404 30.1533 125.096 30.0254L124.035 29.5849L120.482 38.1405ZM143.684 139.898L145.664 141.218L147.343 139.364L145.836 137.523L147.092 136.136L155.31 146.382L153.307 148.593L142.303 141.422L143.684 139.898ZM147.155 142.193L152.675 145.87L148.474 140.738L147.155 142.193ZM147.032 154.859L138.572 145.029L140.116 143.699L148.577 153.529L147.032 154.859ZM133.755 163.899L132.117 164.804L127.597 152.484L130.257 151.015L138.28 161.399L136.48 162.394L130.002 153.886L133.755 163.899ZM121.096 163.595L120.454 161.857L123.078 160.887L121.761 157.324L118.459 158.545L117.817 156.807L123.03 154.88L127.527 167.045L122.314 168.972L121.671 167.234L124.973 166.014L123.72 162.625L121.096 163.595ZM110.351 158.762C110.466 158.952 110.554 159.136 110.615 159.313C110.677 159.489 110.756 159.813 110.853 160.285L111.263 162.282C111.382 162.862 111.569 163.253 111.825 163.452C112.082 163.664 112.441 163.723 112.901 163.629L113.591 163.487L112.529 158.315L114.525 157.905L117.134 170.609L114.121 171.228C113.092 171.439 112.293 171.351 111.721 170.964C111.153 170.589 110.766 169.905 110.563 168.913L110.358 167.915C110.089 166.608 110.348 165.66 111.135 165.069C110.624 164.973 110.218 164.716 109.918 164.298C109.62 163.893 109.399 163.339 109.255 162.637L108.853 160.677C108.778 160.314 108.705 160.02 108.634 159.795C108.565 159.582 108.46 159.377 108.318 159.179L110.351 158.762ZM113.963 165.302L113.183 165.462C112.784 165.544 112.508 165.708 112.357 165.953C112.194 166.201 112.161 166.567 112.261 167.051L112.518 168.304C112.612 168.763 112.765 169.079 112.977 169.25C113.177 169.423 113.452 169.474 113.803 169.402L114.765 169.204L113.963 165.302ZM104.033 172.848L103.012 159.919L105.044 159.758L106.065 172.687L104.033 172.848ZM94.4045 167.175L94.4663 165.324L97.0958 165.411L97.2743 160.06L99.3112 160.128L98.8788 173.09L93.4902 172.91L93.552 171.058L96.9037 171.17L97.034 167.263L94.4045 167.175ZM86.7796 172.272L88.7238 159.449L90.7388 159.755L88.7947 172.578L86.7796 172.272ZM78.4133 164.752L78.9066 162.966L81.6034 163.711L82.6146 160.05L79.2213 159.113L79.7146 157.327L85.0724 158.807L81.6194 171.308L76.2617 169.828L76.7549 168.043L80.1482 168.98L81.1101 165.497L78.4133 164.752ZM68.3298 166.963C67.3986 166.564 66.807 166.016 66.5552 165.317C66.3034 164.618 66.384 163.786 66.797 162.821L69.3769 156.791C69.7899 155.825 70.3361 155.192 71.0155 154.892C71.6948 154.591 72.5001 154.64 73.4313 155.039L76.3953 156.307L71.2937 168.231L68.3298 166.963ZM73.7927 157.209L72.7366 156.757C72.43 156.626 72.1583 156.617 71.9216 156.731C71.6735 156.839 71.462 157.098 71.2871 157.507L68.6343 163.708C68.4594 164.116 68.4182 164.448 68.5108 164.703C68.5921 164.952 68.786 165.143 69.0926 165.274L70.1488 165.726L73.7927 157.209Z\"\n          fill=\"#436EB6\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_13085_4945\">\n          <rect width=\"195.93\" height=\"195.928\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default AIVerifiedIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAM,QAAO;QAAM,SAAQ;QAAc,MAAK;;0BAC1F,8OAAC;gBAAE,aAAU;;kCACX,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBAAK,GAAE;wBAAoF,MAAK;;;;;;kCACjG,8OAAC;wBAAK,GAAE;wBAA4F,MAAK;;;;;;kCACzG,8OAAC;wBAAK,GAAE;wBAA2F,MAAK;;;;;;kCACxG,8OAAC;wBAAK,GAAE;wBAA0F,MAAK;;;;;;kCACvG,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAS,QAAO;wBAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;AAKrD;uCAEe", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/CheckSecondaryIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction CheckSecondaryIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"19\" height=\"19\" viewBox=\"0 0 24 24\" fill=\"none\">\n      <g clip-path=\"url(#clip0_13082_4925)\">\n        <path\n          d=\"M12 0C5.38346 0 0 5.38346 0 12C0 18.6165 5.38346 24 12 24C18.6165 24 24 18.6165 24 12C24 5.38346 18.6165 0 12 0ZM18.7068 8.84211L11.0376 16.4511C10.5865 16.9023 9.86466 16.9323 9.38346 16.4812L5.32331 12.782C4.84211 12.3308 4.81203 11.5789 5.23308 11.0977C5.68421 10.6165 6.43609 10.5865 6.91729 11.0376L10.1353 13.985L16.9925 7.12782C17.4737 6.64662 18.2256 6.64662 18.7068 7.12782C19.188 7.60902 19.188 8.3609 18.7068 8.84211Z\"\n          fill=\"#CB9932\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_13082_4925\">\n          <rect width=\"24\" height=\"24\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default CheckSecondaryIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,mBAAmB,EAAE,SAAS,EAA0B;IAC/D,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBAAE,aAAU;0BACX,cAAA,8OAAC;oBACC,GAAE;oBACF,MAAK;;;;;;;;;;;0BAGT,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAK,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/StarIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction StarIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"16\" height=\"16\" viewBox=\"0 0 20 19\" fill=\"none\">\n      <path\n        d=\"M15.9184 11.82C15.6594 12.071 15.5404 12.434 15.5994 12.79L16.4884 17.71C16.5634 18.127 16.3874 18.549 16.0384 18.79C15.6964 19.04 15.2414 19.07 14.8684 18.87L10.4394 16.56C10.2854 16.478 10.1144 16.434 9.93939 16.429H9.66839C9.57439 16.443 9.48239 16.473 9.39839 16.519L4.96839 18.84C4.74939 18.95 4.50139 18.989 4.25839 18.95C3.66639 18.838 3.27139 18.274 3.36839 17.679L4.25839 12.759C4.31739 12.4 4.19839 12.035 3.93939 11.78L0.328388 8.28C0.0263875 7.987 -0.0786125 7.547 0.0593875 7.15C0.193388 6.754 0.535388 6.465 0.948388 6.4L5.91839 5.679C6.29639 5.64 6.62839 5.41 6.79839 5.07L8.98839 0.58C9.04039 0.48 9.10739 0.388 9.18839 0.31L9.27839 0.24C9.32539 0.188 9.37939 0.145 9.43939 0.11L9.54839 0.07L9.71839 0H10.1394C10.5154 0.039 10.8464 0.264 11.0194 0.6L13.2384 5.07C13.3984 5.397 13.7094 5.624 14.0684 5.679L19.0384 6.4C19.4584 6.46 19.8094 6.75 19.9484 7.15C20.0794 7.551 19.9664 7.991 19.6584 8.28L15.9184 11.82Z\"\n        fill=\"#CB9932\"\n      />\n    </svg>\n  );\n}\n\nexport default StarIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAA0B;IACrD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBAC5G,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/Success80.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction Success80() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"380\" height=\"200\" viewBox=\"0 0 425 214\" fill=\"none\">\n      <path\n        opacity=\"0.1\"\n        d=\"M0 212.5C4.92702e-06 156.141 22.3883 102.091 62.2398 62.2398C102.091 22.3883 156.141 6.04639e-06 212.5 0C268.858 -6.04639e-06 322.909 22.3883 362.76 62.2398C402.612 102.091 425 156.141 425 212.5L359.54 212.5C359.54 173.502 344.049 136.102 316.473 108.527C288.898 80.9513 251.498 65.4596 212.5 65.4596C173.502 65.4596 136.102 80.9513 108.527 108.527C80.9513 136.102 65.4596 173.502 65.4596 212.5H0Z\"\n        fill=\"#333333\"\n      />\n      <g clipPath=\"url(#paint0_angular_13107_4824_clip_path)\" data-figma-skip-parse=\"true\">\n        <g transform=\"matrix(0.0834602 0.126818 -0.0321848 0.109493 233.75 194.792)\">\n          <foreignObject x=\"-3727.44\" y=\"-3727.44\" width=\"7454.88\" height=\"7454.88\">\n            <div></div>\n          </foreignObject>\n        </g>\n      </g>\n      <path\n        d=\"M0 212.5C3.02164e-06 177.937 8.43091 143.895 24.5617 113.326C40.6925 82.7577 64.0362 56.5853 92.5688 37.0783C121.101 17.5713 153.962 5.31855 188.3 1.38244C222.639 -2.55368 257.419 1.94566 289.626 14.4903L265.868 75.4863C243.582 66.8059 219.516 63.6926 195.755 66.4162C171.994 69.1398 149.256 77.6181 129.513 91.1161C109.77 104.614 93.617 122.724 82.4552 143.876C71.2934 165.028 65.4596 188.584 65.4596 212.5H0Z\"\n        data-figma-gradient-fill='{\"type\":\"GRADIENT_ANGULAR\",\"stops\":[{\"color\":{\"r\":0.45416668057441711,\"g\":0.65826088190078735,\"b\":1.0,\"a\":1.0},\"position\":0.0},{\"color\":{\"r\":0.66666668653488159,\"g\":0.79130434989929199,\"b\":1.0,\"a\":1.0},\"position\":0.47430124878883362},{\"color\":{\"r\":0.36333334445953369,\"g\":0.52660870552062988,\"b\":0.80000001192092896,\"a\":1.0},\"position\":1.0}],\"stopsVar\":[{\"color\":{\"r\":0.45416668057441711,\"g\":0.65826088190078735,\"b\":1.0,\"a\":1.0},\"position\":0.0},{\"color\":{\"r\":0.66666668653488159,\"g\":0.79130434989929199,\"b\":1.0,\"a\":1.0},\"position\":0.47430124878883362},{\"color\":{\"r\":0.36333334445953369,\"g\":0.52660870552062988,\"b\":0.80000001192092896,\"a\":1.0},\"position\":1.0}],\"transform\":{\"m00\":166.92030334472656,\"m01\":-64.369651794433594,\"m02\":182.47467041015625,\"m10\":253.63612365722656,\"m11\":218.98637390136719,\"m12\":-41.519580841064453},\"opacity\":1.0,\"blendMode\":\"NORMAL\",\"visible\":true}'\n      />\n      <path\n        d=\"M157.592 170.275C154.244 170.275 151.33 169.655 148.85 168.415C146.411 167.134 144.51 165.419 143.146 163.269C141.823 161.12 141.162 158.764 141.162 156.201C141.162 153.184 141.968 150.642 143.58 148.575C145.192 146.467 147.527 144.917 150.586 143.925L150.09 145.971C147.899 145.269 146.163 143.905 144.882 141.879C143.642 139.854 143.022 137.622 143.022 135.183C143.022 132.745 143.642 130.575 144.882 128.673C146.122 126.772 147.837 125.284 150.028 124.209C152.219 123.135 154.74 122.597 157.592 122.597C160.403 122.597 162.883 123.135 165.032 124.209C167.223 125.284 168.938 126.772 170.178 128.673C171.459 130.575 172.1 132.745 172.1 135.183C172.1 137.663 171.459 139.916 170.178 141.941C168.938 143.925 167.223 145.289 165.032 146.033L164.598 143.863C167.657 144.938 169.992 146.529 171.604 148.637C173.216 150.704 174.022 153.246 174.022 156.263C174.022 158.826 173.34 161.182 171.976 163.331C170.653 165.439 168.752 167.134 166.272 168.415C163.833 169.655 160.94 170.275 157.592 170.275ZM157.592 163.145C159.204 163.145 160.609 162.856 161.808 162.277C163.007 161.657 163.937 160.81 164.598 159.735C165.259 158.619 165.59 157.359 165.59 155.953C165.59 154.507 165.259 153.267 164.598 152.233C163.937 151.159 163.007 150.332 161.808 149.753C160.609 149.133 159.204 148.823 157.592 148.823C155.98 148.823 154.575 149.133 153.376 149.753C152.219 150.332 151.309 151.159 150.648 152.233C149.987 153.267 149.656 154.507 149.656 155.953C149.656 157.359 149.987 158.619 150.648 159.735C151.309 160.81 152.219 161.657 153.376 162.277C154.575 162.856 155.98 163.145 157.592 163.145ZM157.592 141.693C158.791 141.693 159.845 141.425 160.754 140.887C161.663 140.35 162.366 139.647 162.862 138.779C163.399 137.87 163.668 136.837 163.668 135.679C163.668 134.522 163.399 133.509 162.862 132.641C162.366 131.732 161.663 131.029 160.754 130.533C159.845 129.996 158.791 129.727 157.592 129.727C156.393 129.727 155.319 129.996 154.368 130.533C153.459 131.029 152.735 131.732 152.198 132.641C151.702 133.509 151.454 134.522 151.454 135.679C151.454 136.837 151.702 137.87 152.198 138.779C152.735 139.647 153.459 140.35 154.368 140.887C155.319 141.425 156.393 141.693 157.592 141.693ZM199.122 170.275C195.443 170.275 192.219 169.283 189.45 167.299C186.68 165.315 184.51 162.525 182.94 158.929C181.41 155.333 180.646 151.159 180.646 146.405C180.646 141.611 181.41 137.436 182.94 133.881C184.469 130.327 186.618 127.557 189.388 125.573C192.157 123.589 195.381 122.597 199.06 122.597C202.821 122.597 206.066 123.589 208.794 125.573C211.563 127.557 213.733 130.347 215.304 133.943C216.874 137.498 217.66 141.652 217.66 146.405C217.66 151.159 216.874 155.333 215.304 158.929C213.774 162.484 211.625 165.274 208.856 167.299C206.086 169.283 202.842 170.275 199.122 170.275ZM199.184 162.587C201.292 162.587 203.09 161.947 204.578 160.665C206.066 159.343 207.202 157.483 207.988 155.085C208.814 152.647 209.228 149.753 209.228 146.405C209.228 143.057 208.814 140.185 207.988 137.787C207.202 135.349 206.045 133.489 204.516 132.207C203.028 130.926 201.209 130.285 199.06 130.285C196.993 130.285 195.216 130.926 193.728 132.207C192.24 133.489 191.082 135.349 190.256 137.787C189.47 140.185 189.078 143.057 189.078 146.405C189.078 149.712 189.47 152.585 190.256 155.023C191.082 157.421 192.24 159.281 193.728 160.603C195.257 161.926 197.076 162.587 199.184 162.587ZM230.556 169.531L268.004 123.341H276.684L239.236 169.531H230.556ZM237.81 146.777C235.496 146.777 233.408 146.24 231.548 145.165C229.73 144.049 228.283 142.582 227.208 140.763C226.134 138.945 225.596 136.919 225.596 134.687C225.596 132.455 226.134 130.43 227.208 128.611C228.283 126.751 229.73 125.284 231.548 124.209C233.408 123.135 235.496 122.597 237.81 122.597C240.084 122.597 242.13 123.135 243.948 124.209C245.767 125.284 247.214 126.751 248.288 128.611C249.363 130.43 249.9 132.455 249.9 134.687C249.9 136.919 249.363 138.945 248.288 140.763C247.214 142.582 245.767 144.049 243.948 145.165C242.13 146.24 240.084 146.777 237.81 146.777ZM237.748 140.515C238.988 140.515 240.022 140.247 240.848 139.709C241.675 139.131 242.295 138.407 242.708 137.539C243.163 136.63 243.39 135.679 243.39 134.687C243.39 133.654 243.163 132.703 242.708 131.835C242.295 130.967 241.675 130.244 240.848 129.665C240.022 129.087 238.988 128.797 237.748 128.797C236.591 128.797 235.578 129.087 234.71 129.665C233.884 130.244 233.243 130.988 232.788 131.897C232.334 132.765 232.106 133.695 232.106 134.687C232.106 135.638 232.334 136.568 232.788 137.477C233.243 138.387 233.884 139.131 234.71 139.709C235.578 140.247 236.591 140.515 237.748 140.515ZM269.244 170.275C266.971 170.275 264.904 169.738 263.044 168.663C261.226 167.547 259.779 166.08 258.704 164.261C257.63 162.443 257.092 160.417 257.092 158.185C257.092 155.953 257.63 153.928 258.704 152.109C259.779 150.249 261.226 148.782 263.044 147.707C264.904 146.633 266.971 146.095 269.244 146.095C271.559 146.095 273.626 146.633 275.444 147.707C277.263 148.782 278.71 150.249 279.784 152.109C280.859 153.928 281.396 155.953 281.396 158.185C281.396 160.417 280.859 162.443 279.784 164.261C278.71 166.08 277.263 167.547 275.444 168.663C273.626 169.738 271.559 170.275 269.244 170.275ZM269.244 164.013C270.443 164.013 271.456 163.745 272.282 163.207C273.15 162.629 273.791 161.905 274.204 161.037C274.659 160.128 274.886 159.177 274.886 158.185C274.886 157.152 274.659 156.201 274.204 155.333C273.791 154.465 273.15 153.742 272.282 153.163C271.456 152.585 270.443 152.295 269.244 152.295C268.087 152.295 267.074 152.585 266.206 153.163C265.38 153.742 264.739 154.486 264.284 155.395C263.83 156.263 263.602 157.193 263.602 158.185C263.602 159.136 263.83 160.066 264.284 160.975C264.739 161.885 265.38 162.629 266.206 163.207C267.074 163.745 268.087 164.013 269.244 164.013Z\"\n        fill=\"#333333\"\n      />\n      <path\n        d=\"M171.645 208.819C170.365 208.819 169.181 208.587 168.093 208.123C167.005 207.659 166.053 207.011 165.237 206.179C164.421 205.347 163.781 204.371 163.317 203.251C162.869 202.131 162.645 200.907 162.645 199.579C162.645 198.251 162.861 197.027 163.293 195.907C163.741 194.771 164.365 193.795 165.165 192.979C165.981 192.147 166.933 191.507 168.021 191.059C169.109 190.595 170.293 190.363 171.573 190.363C172.853 190.363 173.997 190.579 175.005 191.011C176.029 191.443 176.893 192.019 177.597 192.739C178.301 193.443 178.805 194.219 179.109 195.067L176.205 196.459C175.869 195.531 175.301 194.779 174.501 194.203C173.701 193.627 172.725 193.339 171.573 193.339C170.453 193.339 169.461 193.603 168.597 194.131C167.749 194.659 167.085 195.387 166.605 196.315C166.141 197.243 165.909 198.331 165.909 199.579C165.909 200.827 166.149 201.923 166.629 202.867C167.125 203.795 167.805 204.523 168.669 205.051C169.533 205.579 170.525 205.843 171.645 205.843C172.557 205.843 173.397 205.667 174.165 205.315C174.933 204.947 175.549 204.435 176.013 203.779C176.477 203.107 176.709 202.315 176.709 201.403V200.035L178.197 201.331H171.573V198.571H179.973V200.371C179.973 201.747 179.741 202.963 179.277 204.019C178.813 205.075 178.181 205.963 177.381 206.683C176.597 207.387 175.709 207.923 174.717 208.291C173.725 208.643 172.701 208.819 171.645 208.819ZM188.676 208.819C187.396 208.819 186.228 208.523 185.172 207.931C184.132 207.339 183.3 206.531 182.676 205.507C182.068 204.483 181.764 203.315 181.764 202.003C181.764 200.691 182.068 199.523 182.676 198.499C183.3 197.475 184.132 196.667 185.172 196.075C186.212 195.483 187.38 195.187 188.676 195.187C189.956 195.187 191.116 195.483 192.156 196.075C193.196 196.667 194.02 197.475 194.628 198.499C195.252 199.507 195.564 200.675 195.564 202.003C195.564 203.315 195.252 204.483 194.628 205.507C194.004 206.531 193.172 207.339 192.132 207.931C191.092 208.523 189.94 208.819 188.676 208.819ZM188.676 205.939C189.38 205.939 189.996 205.771 190.524 205.435C191.068 205.099 191.492 204.635 191.796 204.043C192.116 203.435 192.276 202.755 192.276 202.003C192.276 201.235 192.116 200.563 191.796 199.987C191.492 199.395 191.068 198.931 190.524 198.595C189.996 198.243 189.38 198.067 188.676 198.067C187.956 198.067 187.324 198.243 186.78 198.595C186.236 198.931 185.804 199.395 185.484 199.987C185.18 200.563 185.028 201.235 185.028 202.003C185.028 202.755 185.18 203.435 185.484 204.043C185.804 204.635 186.236 205.099 186.78 205.435C187.324 205.771 187.956 205.939 188.676 205.939ZM204.332 208.819C203.052 208.819 201.884 208.523 200.828 207.931C199.788 207.339 198.956 206.531 198.332 205.507C197.724 204.483 197.42 203.315 197.42 202.003C197.42 200.691 197.724 199.523 198.332 198.499C198.956 197.475 199.788 196.667 200.828 196.075C201.868 195.483 203.036 195.187 204.332 195.187C205.612 195.187 206.772 195.483 207.812 196.075C208.852 196.667 209.676 197.475 210.284 198.499C210.908 199.507 211.22 200.675 211.22 202.003C211.22 203.315 210.908 204.483 210.284 205.507C209.66 206.531 208.828 207.339 207.788 207.931C206.748 208.523 205.596 208.819 204.332 208.819ZM204.332 205.939C205.036 205.939 205.652 205.771 206.18 205.435C206.724 205.099 207.148 204.635 207.452 204.043C207.772 203.435 207.932 202.755 207.932 202.003C207.932 201.235 207.772 200.563 207.452 199.987C207.148 199.395 206.724 198.931 206.18 198.595C205.652 198.243 205.036 198.067 204.332 198.067C203.612 198.067 202.98 198.243 202.436 198.595C201.892 198.931 201.46 199.395 201.14 199.987C200.836 200.563 200.684 201.235 200.684 202.003C200.684 202.755 200.836 203.435 201.14 204.043C201.46 204.635 201.892 205.099 202.436 205.435C202.98 205.771 203.612 205.939 204.332 205.939ZM219.677 208.819C218.413 208.819 217.285 208.523 216.293 207.931C215.301 207.323 214.517 206.499 213.941 205.459C213.365 204.419 213.077 203.267 213.077 202.003C213.077 200.723 213.365 199.571 213.941 198.547C214.517 197.523 215.301 196.707 216.293 196.099C217.301 195.491 218.421 195.187 219.653 195.187C220.645 195.187 221.525 195.387 222.293 195.787C223.077 196.171 223.693 196.715 224.141 197.419L223.661 198.067V190.363H226.805V208.531H223.829V206.011L224.165 206.635C223.717 207.339 223.093 207.883 222.293 208.267C221.493 208.635 220.621 208.819 219.677 208.819ZM220.013 205.939C220.733 205.939 221.365 205.771 221.909 205.435C222.453 205.099 222.877 204.635 223.181 204.043C223.501 203.451 223.661 202.771 223.661 202.003C223.661 201.251 223.501 200.579 223.181 199.987C222.877 199.379 222.453 198.907 221.909 198.571C221.365 198.235 220.733 198.067 220.013 198.067C219.309 198.067 218.677 198.243 218.117 198.595C217.557 198.931 217.117 199.395 216.797 199.987C216.493 200.563 216.341 201.235 216.341 202.003C216.341 202.771 216.493 203.451 216.797 204.043C217.117 204.635 217.557 205.099 218.117 205.435C218.677 205.771 219.309 205.939 220.013 205.939ZM234.259 208.531V190.651H246.187V193.531H237.523V198.475H245.107V201.355H237.523V208.531H234.259ZM248.058 208.531V195.475H251.202V208.531H248.058ZM248.058 194.011V190.651H251.202V194.011H248.058ZM260.057 208.675C258.585 208.675 257.441 208.275 256.625 207.475C255.825 206.659 255.425 205.515 255.425 204.043V198.283H253.169V195.475H253.409C254.049 195.475 254.545 195.307 254.897 194.971C255.249 194.635 255.425 194.147 255.425 193.507V192.499H258.569V195.475H261.569V198.283H258.569V203.875C258.569 204.307 258.641 204.675 258.785 204.979C258.945 205.283 259.185 205.515 259.505 205.675C259.841 205.835 260.265 205.915 260.777 205.915C260.889 205.915 261.017 205.907 261.161 205.891C261.321 205.875 261.473 205.859 261.617 205.843V208.531C261.393 208.563 261.137 208.595 260.849 208.627C260.561 208.659 260.297 208.675 260.057 208.675Z\"\n        fill=\"#333333\"\n      />\n      <defs>\n        <clipPath id=\"paint0_angular_13107_4824_clip_path\">\n          <path d=\"M0 212.5C3.02164e-06 177.937 8.43091 143.895 24.5617 113.326C40.6925 82.7577 64.0362 56.5853 92.5688 37.0783C121.101 17.5713 153.962 5.31855 188.3 1.38244C222.639 -2.55368 257.419 1.94566 289.626 14.4903L265.868 75.4863C243.582 66.8059 219.516 63.6926 195.755 66.4162C171.994 69.1398 149.256 77.6181 129.513 91.1161C109.77 104.614 93.617 122.724 82.4552 143.876C71.2934 165.028 65.4596 188.584 65.4596 212.5H0Z\" />\n        </clipPath>\n      </defs>\n    </svg>\n  );\n}\n\nexport default Success80;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAM,QAAO;QAAM,SAAQ;QAAc,MAAK;;0BAC1F,8OAAC;gBACC,SAAQ;gBACR,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBAAE,UAAS;gBAA4C,yBAAsB;0BAC5E,cAAA,8OAAC;oBAAE,WAAU;8BACX,cAAA,8OAAC;wBAAc,GAAE;wBAAW,GAAE;wBAAW,OAAM;wBAAU,QAAO;kCAC9D,cAAA,8OAAC;;;;;;;;;;;;;;;;;;;;0BAIP,8OAAC;gBACC,GAAE;gBACF,4BAAyB;;;;;;0BAE3B,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/conductInterview/CandidateProfile.tsx"], "sourcesContent": ["import { use, useEffect, useState } from \"react\";\nimport Button from \"@/components/formElements/Button\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\nimport PreviewResumeIcon from \"@/components/svgComponents/PreviewResumeIcon\";\nimport Image from \"next/image\";\nimport user from \"../../../../public/assets/images/user.png\";\nimport style from \"../../../styles/conductInterview.module.scss\";\nimport { fetchCandidateProfile, updateJobApplicationStatus } from \"@/services/CandidatesServices/candidatesApplicationServices\";\n// import candidateProfile from \"../../../../public/assets/images/doctor-strange.png\";\n\n//  Import the interface to type the API response\nimport type { CandidateProfileResponse } from \"@/interfaces/candidatesInterface\";\nimport AiMarkIcon from \"@/components/svgComponents/AiMarkIcon\";\nimport AIVerifiedIcon from \"@/components/svgComponents/AIVerifiedIcon\";\nimport CheckSecondaryIcon from \"@/components/svgComponents/CheckSecondaryIcon\";\nimport StarIcon from \"@/components/svgComponents/StarIcon\";\nimport Success80 from \"@/components/svgComponents/Success80\";\nimport { toastMessageError, toTitleCase } from \"@/utils/helper\";\nimport { useTranslations } from \"use-intl\";\nimport { useRouter } from \"next/navigation\";\nimport { toastMessageSuccess } from \"@/utils/helper\";\nimport Loader from \"@/components/loader/Loader\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\nconst CandidateProfile = ({ params }: { params: Promise<{ candidateId: string }> }) => {\n  const router = useRouter();\n  const [selectedTab, setSelectedTab] = useState(true);\n  const [activeSkillTab, setActiveSkillTab] = useState(\"Strengths\");\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const t = useTranslations();\n\n  //  Use the imported interface in useState to strongly type the candidate profile\n  const [candidateProfileData, setCandidateProfileData] = useState<CandidateProfileResponse | null>(null);\n\n  const paramsPromise = use(params);\n  const candidateId = paramsPromise.candidateId;\n\n  const handleTabClick = () => {\n    setSelectedTab(!selectedTab);\n  };\n\n  // Handle candidate rejection\n  const handleHireRejectCandidate = async (status: string) => {\n    if (!candidateProfileData) return;\n    setIsProcessing(true);\n\n    try {\n      const response = await updateJobApplicationStatus(\n        Number(candidateProfileData.jobApplicationId), // Using candidate ID as job application ID\n        status\n      );\n\n      if (response?.data?.success) {\n        toastMessageSuccess(t(response?.data?.message));\n        // Refresh candidate data\n        getCandidateProfile();\n      } else {\n        toastMessageError(t(response?.data?.message));\n      }\n    } catch (error) {\n      console.error(\"Error updating job application status:\", error);\n      toastMessageError(t(\"something_went_wrong\"));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const getCandidateProfile = async () => {\n    try {\n      const response = await fetchCandidateProfile(candidateId);\n      if (response?.data?.success) {\n        setCandidateProfileData(response.data.data); //  Save typed data\n      } else {\n        toastMessageError(t(response?.data?.message));\n      }\n    } catch {\n      toastMessageError(t(\"something_went_wrong\"));\n    }\n  };\n\n  useEffect(() => {\n    getCandidateProfile();\n  }, []);\n\n  return (\n    <div className={style.conduct_interview_page}>\n      <div className=\"container\">\n        <div className=\"common-page-header\">\n          <div className=\"common-page-head-section\">\n            <div className=\"main-heading\">\n              <h2>\n                <Button className=\"clear-btn p-0 m-0\">\n                  <BackArrowIcon\n                    onClick={() => {\n                      router.back();\n                    }}\n                  />\n                </Button>{\" \"}\n                Candidate <span>Profile </span>\n              </h2>\n\n              {/* Conditionally render resume preview button if resumeLink exists */}\n              {candidateProfileData?.resumeLink && (\n                <a\n                  href={candidateProfileData.resumeLink}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"theme-btn clear-btn text-btn primary p-0 m-0\"\n                >\n                  <PreviewResumeIcon className=\"me-2\" />\n                  Preview Candidate Resume\n                </a>\n              )}\n            </div>\n          </div>\n        </div>\n        <div className=\"inner-section profile-section\">\n          <div className=\"candidate-profile\">\n            <Image\n              src={candidateProfileData?.imageUrl || \"\"}\n              alt=\"candidate image\"\n              className=\"candidate-image\"\n              width={100}\n              height={100}\n              priority={true}\n            />\n            <div className=\"candidate-info\">\n              {/*  Use candidateName from API */}\n              <h3 className=\"candidate-name\">{toTitleCase(candidateProfileData?.candidateName || \"-\")}</h3>\n\n              <div className=\"info-container\">\n                <div className=\"info-item\">\n                  <p className=\"info-title\">Post Applied For</p>\n                  {/* Use jobTitle from API */}\n                  <p className=\"info-value\">{candidateProfileData?.jobTitle || \"-\"}</p>\n                </div>\n                <div className=\"info-item\">\n                  <p className=\"info-title\">Departement</p>\n                  <p className=\"info-value\">{candidateProfileData?.department || \"-\"}</p>\n                </div>\n                <div className=\"info-item\">\n                  <p className=\"info-title\">Rounds Cleared</p>\n                  <p className=\"info-value\">{candidateProfileData?.roundNumber || 0}</p>\n                </div>\n                <div className=\"info-item\">\n                  <p className=\"info-title\">Resume Approved By</p>\n                  <p className=\"info-value with-img\">\n                    <Image src={user} alt=\"Interviewer avatar\" />\n                    {/*  Use interviewerName from API */}\n                    {candidateProfileData?.interviewerName || \"-\"}\n                  </p>\n                </div>\n                <div className=\"button-align\">\n                  <Button\n                    className=\"primary-btn rounded-md minWidth\"\n                    onClick={() => handleHireRejectCandidate(APPLICATION_STATUS.HIRED)}\n                    disabled={isProcessing || !candidateProfileData}\n                  >\n                    {/* {isProcessing ? Hire : </Loader>} */}\n                    {isProcessing ? <Loader /> : \"Hire\"}\n                  </Button>\n                  <Button\n                    className=\"dark-outline-btn rounded-md minWidth\"\n                    onClick={() => handleHireRejectCandidate(APPLICATION_STATUS.FINAL_REJECT)}\n                    disabled={isProcessing || !candidateProfileData}\n                  >\n                    {isProcessing ? <Loader /> : \"Reject\"}\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"common-tab mb-5\">\n            <li className={selectedTab ? \"active\" : \"\"} onClick={handleTabClick}>\n              Skill-Specific Assessment\n            </li>\n            <li className={!selectedTab ? \"active\" : \"\"} onClick={handleTabClick}>\n              Interview History\n            </li>\n          </div>\n          {selectedTab && (\n            <div className=\"assessment-content\">\n              <div className=\"row g-4\">\n                <div className=\"col-md-4\">\n                  <Success80 />\n                </div>\n                <div className=\"col-md-8\">\n                  <div className=\"summary-text-card row\">\n                    <div className=\"col-md-9\">\n                      <h3 className=\"sub-tittle mt-0\">\n                        <AiMarkIcon className=\"me-2\" /> AI Summary\n                      </h3>\n                      <ul className=\"check-list\">\n                        <li>\n                          <CheckSecondaryIcon className=\"me-2\" /> Strong foundational skills, emotional intelligence.\n                        </li>\n                        <li>\n                          <CheckSecondaryIcon className=\"me-2\" /> Strong foundational skills, emotional intelligence.\n                        </li>\n                        <li>\n                          <CheckSecondaryIcon className=\"me-2\" /> Strong foundational skills, emotional intelligence.\n                        </li>\n                      </ul>\n                    </div>\n                    <div className=\"col-md-3\">\n                      <AIVerifiedIcon />\n                    </div>\n                  </div>\n                </div>\n              </div>\n              {/* next design */}\n              <div className=\"row g-4\">\n                <div className=\"col-lg-4\">\n                  <div className=\"summary-text-card skills-score-card\">\n                    <h3 className=\"sub-tittle mt-0\">Skills Score</h3>\n                    <ul className=\"skills-list\">\n                      <li className=\"skills-item\">\n                        <span className=\"skill-name\">Hard Skills</span>\n                        <span className=\"skill-rating\">\n                          <StarIcon /> 7/10\n                        </span>\n                      </li>\n                      <li className=\"skills-item\">\n                        <span className=\"skill-name\">Work Ethic</span>\n                        <span className=\"skill-rating\">\n                          <StarIcon /> 8/10\n                        </span>\n                      </li>\n                      <li className=\"skills-item\">\n                        <span className=\"skill-name\">Humility</span>\n                        <span className=\"skill-rating\">\n                          <StarIcon /> 6/10\n                        </span>\n                      </li>\n                      <li className=\"skills-item\">\n                        <span className=\"skill-name\">Drive</span>\n                        <span className=\"skill-rating\">\n                          <StarIcon /> 9/10\n                        </span>\n                      </li>\n                      <li className=\"skills-item\">\n                        <span className=\"skill-name\">Confidence</span>\n                        <span className=\"skill-badge\">Extreme</span>\n                      </li>\n                      <li className=\"skills-item\">\n                        <span className=\"skill-name\">Resourcefulness</span>\n                        <span className=\"skill-rating\">\n                          <StarIcon /> 9/10\n                        </span>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n                <div className=\"col-lg-8\">\n                  <div className=\"summary-text-card skills-summary-card\">\n                    <h3 className=\"sub-tittle mt-0\">Skills Summary</h3>\n                    <div className=\"skills-tags\">\n                      <span className={`skill-tag ${activeSkillTab === \"Strengths\" ? \"active\" : \"\"}`} onClick={() => setActiveSkillTab(\"Strengths\")}>\n                        Strengths\n                      </span>\n                      <span className={`skill-tag ${activeSkillTab === \"WorkEthic\" ? \"active\" : \"\"}`} onClick={() => setActiveSkillTab(\"WorkEthic\")}>\n                        Work Ethic\n                      </span>\n                      <span\n                        className={`skill-tag ${activeSkillTab === \"Communication\" ? \"active\" : \"\"}`}\n                        onClick={() => setActiveSkillTab(\"Communication\")}\n                      >\n                        Communication\n                      </span>\n                      <span className={`skill-tag ${activeSkillTab === \"Curiosity\" ? \"active\" : \"\"}`} onClick={() => setActiveSkillTab(\"Curiosity\")}>\n                        Curiosity\n                      </span>\n                      <span\n                        className={`skill-tag ${activeSkillTab === \"Decisiveness\" ? \"active\" : \"\"}`}\n                        onClick={() => setActiveSkillTab(\"Decisiveness\")}\n                      >\n                        Decisiveness\n                      </span>\n                    </div>\n\n                    <div className=\"strengths-gaps\">\n                      {activeSkillTab === \"Strengths\" && (\n                        <div className=\"row\">\n                          <div className=\"col-md-7\">\n                            <h4 className=\"skill-sub-title\">Strengths</h4>\n                            <ul className=\"strengths\">\n                              <li className=\"strength-item\">Strong note-taking skills (uses OneNote and a notebook).</li>\n                              <li className=\"strength-item\">Strong note-taking skills (uses OneNote and a notebook).</li>\n                              <li className=\"strength-item\">Strong note-taking skills (uses OneNote and a notebook).</li>\n                            </ul>\n                            <h4 className=\"skill-sub-title\">Potential Gaps</h4>\n                            <ul className=\"strengths\">\n                              <li className=\"strength-item\">\n                                Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.\n                              </li>\n                            </ul>\n                          </div>\n                          <div className=\"col-md-5\">\n                            <div className=\"probability-card\">\n                              <h4 className=\"skill-sub-title\">Skill Success Probability</h4>\n                              <div className=\"progress-container\">\n                                <h3 className=\"ms-2 fw-bold\">80%</h3>\n                                <div className=\"probability-bar\">\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar filled\" />\n                                  <div className=\"bar\" />\n                                  <div className=\"bar\" />\n                                </div>\n                              </div>\n                              <div className=\"insight-card\">\n                                <h4 className=\"insight-title\">Insight</h4>\n                                <p className=\"insight-text\">The candidate has a strong foundation in [specific area].</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n                      {activeSkillTab === \"WorkEthic\" && (\n                        <div>\n                          <h4 className=\"skill-sub-title\">Work Ethic</h4>\n                          <ul className=\"strengths\">\n                            <li className=\"strength-item\">Lacks experience in [specific area].</li>\n                            <li className=\"strength-item\">Could improve on [specific skill].</li>\n                          </ul>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                <div className=\"col-12\">\n                  <div className=\"summary-text-card improvement-areas-card\">\n                    <h3 className=\"sub-tittle mt-0\">Improvement Areas</h3>\n                    <div className=\"row g-3\">\n                      <div className=\"col-md-4\">\n                        <div className=\"improvement-card\">\n                          <h4 className=\"title\">Time Management</h4>\n                          <p className=\"description\">\n                            Could benefit from using more advanced time management techniques to handle multiple projects simultaneously.\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"col-md-4\">\n                        <div className=\"improvement-card\">\n                          <h4 className=\"title\">Software Proficiency</h4>\n                          <p className=\"description\">\n                            While proficient in basics tools, advanced training in [specific software] would be beneficial.\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"col-md-4\">\n                        <div className=\"improvement-card\">\n                          <h4 className=\"title\">Communication</h4>\n                          <p className=\"description\">\n                            Could benefit from using more advanced time management techniques to handle multiple projects simultaneously.\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              {/* end next design */}\n            </div>\n          )}\n          {!selectedTab && (\n            <div className=\"history-content\">\n              <div className=\"interview-summary\">\n                <div className=\"summary-header\">\n                  <h1 className=\"summary-heading\">Round 1 Summary</h1>\n                </div>\n                <div className=\"interviewer\">\n                  <h2 className=\"summary-title\">Interview By</h2>\n                  <div className=\"interviewer-info\">\n                    <Image src={user} alt=\"Interviewer avatar\" className=\"interviewer-avatar\" />\n                    <span className=\"interviewer-name\">Aaron Salko</span>\n                  </div>\n                </div>\n                <div className=\"summary-scores\">\n                  <h2 className=\"summary-title\">Scores</h2>\n                  <div className=\"score-btns\">\n                    <Button className=\"secondary-btn rounded-md px-3 py-3\">Hard Skills : 07</Button>\n                    <Button className=\"secondary-btn rounded-md px-3 py-3\">Work Ethic : 08</Button>\n                    <Button className=\"secondary-btn rounded-md px-3 py-3\">Humility : 06</Button>\n                    <Button className=\"secondary-btn rounded-md px-3 py-3\">Drive : 09</Button>\n                    <Button className=\"secondary-btn rounded-md px-3 py-3\">Confidence : Extreme</Button>\n                  </div>\n                </div>\n                <div className=\"summary-highlights\">\n                  <h2 className=\"summary-title\">Highlights</h2>\n                  <ul className=\"highlight-list\">\n                    <li className=\"highlight-item\">Proficient in managing schedules and using tools like [specific tools].</li>\n                    <li className=\"highlight-item\">Adaptable under high workload and challenging scenarios.</li>\n                    <li className=\"highlight-item\">Ensures compliance with safety and organizational standards.</li>\n                    <li className=\"highlight-item\">Proactive in taking ownership beyond assigned tasks.</li>\n                  </ul>\n                </div>\n              </div>\n              <div className=\"interview-summary\">\n                <div className=\"summary-header\">\n                  <h1 className=\"summary-heading\">Your Performance Feedback</h1>\n                </div>\n                <div className=\"interviewer\">\n                  <div className=\"interviewer-info large\">\n                    <Image src={user} alt=\"Interviewer avatar\" className=\"interviewer-avatar\" />\n                    <span className=\"interviewer-name\">Aaron Salko</span>\n                  </div>\n                </div>\n                <div className=\"summary-highlights\">\n                  <h2 className=\"summary-title\">Highlights</h2>\n                  <ul className=\"highlight-list\">\n                    <li className=\"highlight-item\">Proficient in managing schedules and using tools like [specific tools].</li>\n                    <li className=\"highlight-item\">Adaptable under high workload and challenging scenarios.</li>\n                    <li className=\"highlight-item\">Ensures compliance with safety and organizational standards.</li>\n                    <li className=\"highlight-item\">Proactive in taking ownership beyond assigned tasks.</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CandidateProfile;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAgD;IAChF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IAExB,iFAAiF;IACjF,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAElG,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;IAC1B,MAAM,cAAc,cAAc,WAAW;IAE7C,MAAM,iBAAiB;QACrB,eAAe,CAAC;IAClB;IAEA,6BAA6B;IAC7B,MAAM,4BAA4B,OAAO;QACvC,IAAI,CAAC,sBAAsB;QAC3B,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAC9C,OAAO,qBAAqB,gBAAgB,GAC5C;YAGF,IAAI,UAAU,MAAM,SAAS;gBAC3B,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,UAAU,MAAM;gBACtC,yBAAyB;gBACzB;YACF,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,wBAAqB,AAAD,EAAE;YAC7C,IAAI,UAAU,MAAM,SAAS;gBAC3B,wBAAwB,SAAS,IAAI,CAAC,IAAI,GAAG,mBAAmB;YAClE,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAM;YACN,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;QACtB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,+JAAA,CAAA,UAAK,CAAC,sBAAsB;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,4IAAA,CAAA,UAAM;4CAAC,WAAU;sDAChB,cAAA,8OAAC,oJAAA,CAAA,UAAa;gDACZ,SAAS;oDACP,OAAO,IAAI;gDACb;;;;;;;;;;;wCAEM;wCAAI;sDACJ,8OAAC;sDAAK;;;;;;;;;;;;gCAIjB,sBAAsB,4BACrB,8OAAC;oCACC,MAAM,qBAAqB,UAAU;oCACrC,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC,wJAAA,CAAA,UAAiB;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;8BAOhD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,sBAAsB,YAAY;oCACvC,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,UAAU;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAG,WAAU;sDAAkB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,sBAAsB,iBAAiB;;;;;;sDAEnF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAa;;;;;;sEAE1B,8OAAC;4DAAE,WAAU;sEAAc,sBAAsB,YAAY;;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAa;;;;;;sEAC1B,8OAAC;4DAAE,WAAU;sEAAc,sBAAsB,cAAc;;;;;;;;;;;;8DAEjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAa;;;;;;sEAC1B,8OAAC;4DAAE,WAAU;sEAAc,sBAAsB,eAAe;;;;;;;;;;;;8DAElE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAa;;;;;;sEAC1B,8OAAC;4DAAE,WAAU;;8EACX,8OAAC,6HAAA,CAAA,UAAK;oEAAC,KAAK,oSAAA,CAAA,UAAI;oEAAE,KAAI;;;;;;gEAErB,sBAAsB,mBAAmB;;;;;;;;;;;;;8DAG9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4IAAA,CAAA,UAAM;4DACL,WAAU;4DACV,SAAS,IAAM,0BAA0B,0IAAA,CAAA,qBAAkB,CAAC,KAAK;4DACjE,UAAU,gBAAgB,CAAC;sEAG1B,6BAAe,8OAAC,sIAAA,CAAA,UAAM;;;;uEAAM;;;;;;sEAE/B,8OAAC,4IAAA,CAAA,UAAM;4DACL,WAAU;4DACV,SAAS,IAAM,0BAA0B,0IAAA,CAAA,qBAAkB,CAAC,YAAY;4DACxE,UAAU,gBAAgB,CAAC;sEAE1B,6BAAe,8OAAC,sIAAA,CAAA,UAAM;;;;uEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAW,cAAc,WAAW;oCAAI,SAAS;8CAAgB;;;;;;8CAGrE,8OAAC;oCAAG,WAAW,CAAC,cAAc,WAAW;oCAAI,SAAS;8CAAgB;;;;;;;;;;;;wBAIvE,6BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gJAAA,CAAA,UAAS;;;;;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC,iJAAA,CAAA,UAAU;wEAAC,WAAU;;;;;;oEAAS;;;;;;;0EAEjC,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;;0FACC,8OAAC,yJAAA,CAAA,UAAkB;gFAAC,WAAU;;;;;;4EAAS;;;;;;;kFAEzC,8OAAC;;0FACC,8OAAC,yJAAA,CAAA,UAAkB;gFAAC,WAAU;;;;;;4EAAS;;;;;;;kFAEzC,8OAAC;;0FACC,8OAAC,yJAAA,CAAA,UAAkB;gFAAC,WAAU;;;;;;4EAAS;;;;;;;;;;;;;;;;;;;kEAI7C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAkB;;;;;;kEAChC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAa;;;;;;kFAC7B,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,+IAAA,CAAA,UAAQ;;;;;4EAAG;;;;;;;;;;;;;0EAGhB,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAa;;;;;;kFAC7B,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,+IAAA,CAAA,UAAQ;;;;;4EAAG;;;;;;;;;;;;;0EAGhB,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAa;;;;;;kFAC7B,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,+IAAA,CAAA,UAAQ;;;;;4EAAG;;;;;;;;;;;;;0EAGhB,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAa;;;;;;kFAC7B,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,+IAAA,CAAA,UAAQ;;;;;4EAAG;;;;;;;;;;;;;0EAGhB,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAa;;;;;;kFAC7B,8OAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAa;;;;;;kFAC7B,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,+IAAA,CAAA,UAAQ;;;;;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAMtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAkB;;;;;;kEAChC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,UAAU,EAAE,mBAAmB,cAAc,WAAW,IAAI;gEAAE,SAAS,IAAM,kBAAkB;0EAAc;;;;;;0EAG/H,8OAAC;gEAAK,WAAW,CAAC,UAAU,EAAE,mBAAmB,cAAc,WAAW,IAAI;gEAAE,SAAS,IAAM,kBAAkB;0EAAc;;;;;;0EAG/H,8OAAC;gEACC,WAAW,CAAC,UAAU,EAAE,mBAAmB,kBAAkB,WAAW,IAAI;gEAC5E,SAAS,IAAM,kBAAkB;0EAClC;;;;;;0EAGD,8OAAC;gEAAK,WAAW,CAAC,UAAU,EAAE,mBAAmB,cAAc,WAAW,IAAI;gEAAE,SAAS,IAAM,kBAAkB;0EAAc;;;;;;0EAG/H,8OAAC;gEACC,WAAW,CAAC,UAAU,EAAE,mBAAmB,iBAAiB,WAAW,IAAI;gEAC3E,SAAS,IAAM,kBAAkB;0EAClC;;;;;;;;;;;;kEAKH,8OAAC;wDAAI,WAAU;;4DACZ,mBAAmB,6BAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FAAkB;;;;;;0FAChC,8OAAC;gFAAG,WAAU;;kGACZ,8OAAC;wFAAG,WAAU;kGAAgB;;;;;;kGAC9B,8OAAC;wFAAG,WAAU;kGAAgB;;;;;;kGAC9B,8OAAC;wFAAG,WAAU;kGAAgB;;;;;;;;;;;;0FAEhC,8OAAC;gFAAG,WAAU;0FAAkB;;;;;;0FAChC,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAG,WAAU;8FAAgB;;;;;;;;;;;;;;;;;kFAKlC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAAkB;;;;;;8FAChC,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAG,WAAU;sGAAe;;;;;;sGAC7B,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;8GACf,8OAAC;oGAAI,WAAU;;;;;;;;;;;;;;;;;;8FAGnB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAG,WAAU;sGAAgB;;;;;;sGAC9B,8OAAC;4FAAE,WAAU;sGAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4DAMrC,mBAAmB,6BAClB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkB;;;;;;kFAChC,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAG,WAAU;0FAAgB;;;;;;0FAC9B,8OAAC;gFAAG,WAAU;0FAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO1C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAkB;;;;;;kEAChC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAQ;;;;;;sFACtB,8OAAC;4EAAE,WAAU;sFAAc;;;;;;;;;;;;;;;;;0EAK/B,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAQ;;;;;;sFACtB,8OAAC;4EAAE,WAAU;sFAAc;;;;;;;;;;;;;;;;;0EAK/B,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAQ;;;;;;sFACtB,8OAAC;4EAAE,WAAU;sFAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAY1C,CAAC,6BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkB;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DAAC,KAAK,oSAAA,CAAA,UAAI;4DAAE,KAAI;4DAAqB,WAAU;;;;;;sEACrD,8OAAC;4DAAK,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4IAAA,CAAA,UAAM;4DAAC,WAAU;sEAAqC;;;;;;sEACvD,8OAAC,4IAAA,CAAA,UAAM;4DAAC,WAAU;sEAAqC;;;;;;sEACvD,8OAAC,4IAAA,CAAA,UAAM;4DAAC,WAAU;sEAAqC;;;;;;sEACvD,8OAAC,4IAAA,CAAA,UAAM;4DAAC,WAAU;sEAAqC;;;;;;sEACvD,8OAAC,4IAAA,CAAA,UAAM;4DAAC,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;sDAG3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAIrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkB;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDAAC,KAAK,oSAAA,CAAA,UAAI;wDAAE,KAAI;wDAAqB,WAAU;;;;;;kEACrD,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;uCAEe", "debugId": null}}, {"offset": {"line": 2399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2405, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/candidate-profile/%5BcandidateId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport CandidateProfile from \"@/components/views/conductInterview/CandidateProfile\";\nimport React from \"react\";\n\nconst page = ({ params }: { params: Promise<{ candidateId: string }> }) => {\n  return (\n    <div>\n      <CandidateProfile params={params} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAgD;IACpE,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,UAAgB;YAAC,QAAQ;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}