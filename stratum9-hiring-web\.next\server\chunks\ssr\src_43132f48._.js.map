{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/screenResumeServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { http } from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { IFormValues } from \"@/interfaces/screenResumeInterfaces\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\n// Define interface for pagination parameters\ninterface PaginationParams {\n  limit?: number;\n  offset?: number;\n  job_id?: number;\n  status?: string;\n  hiring_manager_id?: number;\n  organization_id?: number;\n}\n\n// Define interfaces for API responses\ninterface ManualUploadResponse {\n  success: boolean;\n  message: string;\n  data: {\n    name: string;\n    email: string;\n    gender: string;\n    additional_details: string;\n    resume_file: string;\n    resume_text: string;\n    assessment_file: string;\n    assessment_text: string;\n  };\n}\n\ninterface JobApplication {\n  application_id: number;\n  job_id: number;\n  hiring_manager_id: number;\n  candidate_id: number;\n  candidate_name: string;\n  ai_decision: string;\n  ai_reason: string;\n  status?: string;\n  created_ts: string;\n}\n\ninterface JobApplicationResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication[];\n  pagination: {\n    limit: number;\n    offset: number;\n    totalCount: number;\n    hasMore: boolean;\n  };\n}\n\ninterface ChangeApplicationStatusParams {\n  job_id: number;\n  candidate_id: number;\n  hiring_manager_id: number;\n  status: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];\n  hiring_manager_reason: string;\n}\n\ninterface ChangeApplicationStatusResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication;\n}\n\n/**\n * Upload resume and assessment files and get presigned URLs\n * @param file - The file to upload (resume or assessment)\n * @returns Promise with presigned URL response\n */\nexport const getPresignedUrl = async (file: File): Promise<ApiResponse> => {\n  const formData = new FormData();\n  formData.append(\"file\", file);\n  formData.append(\"fileType\", file.type);\n  formData.append(\"fileName\", file.name);\n\n  return http.post(endpoint.resumeScreen.GET_PRESIGNED_URL, formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n};\n\n/**\n * Upload file to S3 using presigned URL\n * @param presignedUrl - The presigned URL for S3 upload\n * @param file - The file to upload\n * @returns Promise with upload response\n */\nexport const uploadToS3 = async (presignedUrl: string, file: File): Promise<Response> => {\n  return fetch(presignedUrl, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n\n/**\n * Process the file upload to get presigned URL and upload to S3\n * @param file - The file to upload\n * @returns Object with file URL and parsed text\n */\nexport const processFileUpload = async (file: File): Promise<{ fileUrl: string; fileText: string; presignedUrl: string }> => {\n  try {\n    // Get presigned URL\n    const presignedUrlResponse = await getPresignedUrl(file);\n\n    if (!presignedUrlResponse.data) {\n      throw new Error(\"Failed to get presigned URL\");\n    }\n    const responseData = presignedUrlResponse.data;\n\n    // The response might have data nested inside another data property\n    const urlData = responseData.data;\n\n    if (!urlData.presignedUrl || !urlData.fileUrl) {\n      console.error(\"Missing URL information in response:\", urlData);\n      throw new Error(\"Missing URL information in response\");\n    }\n\n    const { presignedUrl, fileUrl, fileText } = urlData;\n\n    // Upload file to S3\n    const uploadResponse = await uploadToS3(presignedUrl, file);\n    if (!uploadResponse.ok) {\n      throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);\n    }\n    // Return the file URL and flag for backend extraction\n    return {\n      fileUrl,\n      fileText: fileText, // Special flag to indicate backend should extract text\n      presignedUrl,\n    };\n  } catch (error) {\n    console.error(\"Error processing file upload:\", error);\n    // Include error details in the console for debugging\n    if (error instanceof Error) {\n      console.error(\"Error message:\", error.message);\n      console.error(\"Error stack:\", error.stack);\n    }\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidate data with resume and assessment\n * @param data - The form values with candidate information\n * @returns Promise with API response\n */\n/**\n * Get all job applications with pagination (not just pending)\n * @param params - Pagination parameters (limit, offset, filters)\n * @returns Promise with job applications response\n */\nexport const getAllPendingJobApplications = async (params: PaginationParams): Promise<ApiResponse<JobApplicationResponse>> => {\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    if (params.limit) queryParams.append(\"limit\", params.limit.toString());\n    // Always include offset parameter, even when it's 0\n    queryParams.append(\"offset\", params.offset !== undefined ? params.offset.toString() : \"0\");\n    if (params.job_id) queryParams.append(\"job_id\", params.job_id.toString());\n    if (params.status) queryParams.append(\"status\", params.status);\n    if (params.hiring_manager_id) queryParams.append(\"hiring_manager_id\", params.hiring_manager_id.toString());\n    if (params.organization_id) queryParams.append(\"organization_id\", params.organization_id.toString());\n\n    // Make API request\n    const url = `${endpoint.resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;\n    return http.get(url);\n  } catch (error) {\n    console.error(\"Error fetching job applications:\", error);\n    throw error;\n  }\n};\n\nexport const uploadManualCandidate = async (data: IFormValues, jobId: number): Promise<ApiResponse<ManualUploadResponse>> => {\n  try {\n    // Process candidates with file uploads\n    const processedCandidates = await Promise.all(\n      data.candidates.map(async (candidate) => {\n        // Process resume file\n        const resumeResult = candidate.resume ? await processFileUpload(candidate.resume as File) : { presignedUrl: \"\", fileUrl: \"\", fileText: \"\" };\n        // Process assessment file\n        const assessmentResult = candidate.assessment\n          ? await processFileUpload(candidate.assessment as File)\n          : { presignedUrl: \"\", fileUrl: \"\", fileText: \"\" };\n        return {\n          name: candidate.name,\n          email: candidate.email,\n          gender: candidate.gender,\n          additional_details: candidate.additionalInfo || \"\",\n          resume_file: resumeResult.presignedUrl,\n          resume_text: resumeResult.fileText,\n          assessment_file: assessmentResult.presignedUrl,\n          assessment_text: assessmentResult.fileText,\n        };\n      })\n    );\n\n    // Create payload for API\n    const payload = {\n      job_id: jobId,\n      candidates: processedCandidates,\n    };\n\n    // Send to backend API\n    return http.post(endpoint.resumeScreen.MANUAL_CANDIDATE_UPLOAD, payload);\n  } catch (error) {\n    console.error(\"Error in uploadManualCandidate:\", error);\n    throw error;\n  }\n};\n\n/**\n * Change the status of a job application (Approve, Reject, or Hold)\n * @param params - Parameters containing job_id, candidate_id, hiring_manager_id, and status\n * @param data - Data containing hiring_manager_reason\n * @returns Promise with API response\n */\nexport const changeApplicationStatus = async (data: ChangeApplicationStatusParams): Promise<ApiResponse<ChangeApplicationStatusResponse>> => {\n  return http.post(endpoint.resumeScreen.CHANGE_APPLICATION_STATUS, data);\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AA0EO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IACrC,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IAErC,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE,UAAU;QAClE,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAQO,MAAM,aAAa,OAAO,cAAsB;IACrD,OAAO,MAAM,cAAc;QACzB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,oBAAoB;QACpB,MAAM,uBAAuB,MAAM,gBAAgB;QAEnD,IAAI,CAAC,qBAAqB,IAAI,EAAE;YAC9B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,eAAe,qBAAqB,IAAI;QAE9C,mEAAmE;QACnE,MAAM,UAAU,aAAa,IAAI;QAEjC,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,OAAO,EAAE;YAC7C,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;QAE5C,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,WAAW,cAAc;QACtD,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,eAAe,MAAM,EAAE;QACzE;QACA,sDAAsD;QACtD,OAAO;YACL;YACA,UAAU;YACV;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,qDAAqD;QACrD,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;YAC7C,QAAQ,KAAK,CAAC,gBAAgB,MAAM,KAAK;QAC3C;QACA,MAAM;IACR;AACF;AAYO,MAAM,+BAA+B,OAAO;IACjD,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACnE,oDAAoD;QACpD,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,CAAC,QAAQ,KAAK;QACtF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACtE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7D,IAAI,OAAO,iBAAiB,EAAE,YAAY,MAAM,CAAC,qBAAqB,OAAO,iBAAiB,CAAC,QAAQ;QACvG,IAAI,OAAO,eAAe,EAAE,YAAY,MAAM,CAAC,mBAAmB,OAAO,eAAe,CAAC,QAAQ;QAEjG,mBAAmB;QACnB,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI;QACjG,OAAO,oHAAA,CAAA,OAAI,CAAC,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OAAO,MAAmB;IAC7D,IAAI;QACF,uCAAuC;QACvC,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,KAAK,UAAU,CAAC,GAAG,CAAC,OAAO;YACzB,sBAAsB;YACtB,MAAM,eAAe,UAAU,MAAM,GAAG,MAAM,kBAAkB,UAAU,MAAM,IAAY;gBAAE,cAAc;gBAAI,SAAS;gBAAI,UAAU;YAAG;YAC1I,0BAA0B;YAC1B,MAAM,mBAAmB,UAAU,UAAU,GACzC,MAAM,kBAAkB,UAAU,UAAU,IAC5C;gBAAE,cAAc;gBAAI,SAAS;gBAAI,UAAU;YAAG;YAClD,OAAO;gBACL,MAAM,UAAU,IAAI;gBACpB,OAAO,UAAU,KAAK;gBACtB,QAAQ,UAAU,MAAM;gBACxB,oBAAoB,UAAU,cAAc,IAAI;gBAChD,aAAa,aAAa,YAAY;gBACtC,aAAa,aAAa,QAAQ;gBAClC,iBAAiB,iBAAiB,YAAY;gBAC9C,iBAAiB,iBAAiB,QAAQ;YAC5C;QACF;QAGF,yBAAyB;QACzB,MAAM,UAAU;YACd,QAAQ;YACR,YAAY;QACd;QAEA,sBAAsB;QACtB,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,uBAAuB,EAAE;IAClE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAQO,MAAM,0BAA0B,OAAO;IAC5C,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,yBAAyB,EAAE;AACpE", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/validations/screenResumeValidations.ts"], "sourcesContent": ["// validations/screenResumeValidations.ts\nimport * as yup from \"yup\";\nimport { CANDIDATE_NAME_REGEX } from \"@/utils/validationSchema\";\n\nconst candidateSchema = (t: (key: string) => string) =>\n  yup.object().shape({\n    name: yup.string().trim().required(t(\"name_required\")).min(2, t(\"name_min\")).max(50, t(\"name_max\")),\n    email: yup.string().trim().required(t(\"email_required\")).email(t(\"email_valid\")).max(50, t(\"email_max\")),\n    gender: yup.string().required(t(\"gender_required\")),\n    resume: yup.mixed().required(t(\"resume_required\")),\n    assessment: yup.mixed().optional().nullable(),\n    additionalInfo: yup.string().trim().optional().max(200, t(\"additional_max\")),\n  });\n\nexport const formSchemaValidation = (t: (key: string) => string) =>\n  yup.object({\n    candidates: yup.array().of(candidateSchema(t)).min(1, t(\"at_least_one_candidate\")),\n  });\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;AACzC;;AAGA,MAAM,kBAAkB,CAAC,IACvB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,KAAK,CAAC;QACjB,MAAM,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,EAAE,kBAAkB,GAAG,CAAC,GAAG,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE;QACvF,OAAO,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,CAAC,EAAE,mBAAmB,KAAK,CAAC,EAAE,gBAAgB,GAAG,CAAC,IAAI,EAAE;QAC3F,QAAQ,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,QAAQ,CAAC,EAAE;QAChC,QAAQ,CAAA,GAAA,mIAAA,CAAA,QAAS,AAAD,IAAI,QAAQ,CAAC,EAAE;QAC/B,YAAY,CAAA,GAAA,mIAAA,CAAA,QAAS,AAAD,IAAI,QAAQ,GAAG,QAAQ;QAC3C,gBAAgB,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,IAAI,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC,KAAK,EAAE;IAC5D;AAEK,MAAM,uBAAuB,CAAC,IACnC,CAAA,GAAA,mIAAA,CAAA,SAAU,AAAD,EAAE;QACT,YAAY,CAAA,GAAA,mIAAA,CAAA,QAAS,AAAD,IAAI,EAAE,CAAC,gBAAgB,IAAI,GAAG,CAAC,GAAG,EAAE;IAC1D", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/screenResumeConstant.ts"], "sourcesContent": ["export const GENDER_OPTIONS = [\n  { value: \"Male\", label: \"Male\" },\n  { value: \"Female\", label: \"Female\" },\n];\n\nexport enum InterviewTabType {\n  UPCOMING = \"UpcomingInterviews\",\n  PAST = \"PastInterviews\",\n}\n\nexport const APPLICATION_UPDATE_STATUS = {\n  PROMOTED: \"Promoted\",\n  DEMOTED: \"Demoted\",\n};\n\nexport type APPLICATION_UPDATE_STATUS = (typeof APPLICATION_UPDATE_STATUS)[keyof typeof APPLICATION_UPDATE_STATUS];\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAEM,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,MAAM,4BAA4B;IACvC,UAAU;IACV,SAAS;AACX", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/HoldIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype HoldIconProps = {\n  className?: string;\n  PrimaryColor?: boolean;\n};\n\nfunction HoldIcon({ className, PrimaryColor }: HoldIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" className={className}>\n      <path\n        d=\"M20.0746 0H14.0859C13.827 0 13.6172 0.209859 13.6172 0.46875C13.6172 0.727641 13.827 0.9375 14.0859 0.9375H20.0746C20.932 0.9375 21.6296 1.63505 21.6296 2.49239V21.5076C21.6296 22.365 20.932 23.0625 20.0746 23.0625H3.92505C3.06766 23.0625 2.37012 22.3649 2.37012 21.5075V2.49244C2.37012 1.63505 3.06766 0.9375 3.92505 0.9375H9.96129C10.2202 0.9375 10.43 0.727641 10.43 0.46875C10.43 0.209859 10.2202 0 9.96129 0H3.92505C2.55073 0 1.43262 1.11811 1.43262 2.49244V21.5076C1.43262 22.8819 2.55073 24 3.92505 24H20.0746C21.4489 24 22.5671 22.8819 22.5671 21.5076V2.49239C22.5671 1.11811 21.4489 0 20.0746 0Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M8.53423 4.54633V7.32878C8.07003 6.94197 7.49131 6.73145 6.88053 6.73145C6.62164 6.73145 6.41178 6.94131 6.41178 7.2002V10.7753C6.33068 13.8414 8.90553 16.5269 11.9797 16.528C15.0649 16.528 17.6343 13.8204 17.5475 10.7477V5.84359C17.5475 5.03495 16.8911 4.37702 16.0841 4.37702H16.0752C15.8895 4.37702 15.7118 4.41184 15.5483 4.47531C15.5211 3.66695 14.9015 3.06419 14.0858 3.06419C13.8903 3.06419 13.7036 3.10281 13.5329 3.1728C13.4017 2.47075 12.7994 1.95508 12.0548 1.95508C11.3246 1.95508 10.7143 2.47919 10.5789 3.17167C10.4058 3.10356 10.2146 3.06648 10.0113 3.06648C9.19686 3.06658 8.53423 3.73037 8.53423 4.54633ZM10.5508 4.54633C10.5508 4.57023 10.5507 9.1585 10.5507 9.1585C10.5507 9.41739 10.7606 9.62725 11.0195 9.62725C11.2784 9.62725 11.4882 9.41739 11.4882 9.1585L11.4883 3.46216C11.4883 3.14809 11.7424 2.89262 12.0547 2.89262C12.3834 2.89262 12.6219 3.13216 12.6219 3.46216L12.6211 9.01787C12.6211 9.27677 12.8309 9.48662 13.0898 9.48662C13.3487 9.48662 13.5586 9.27677 13.5586 9.01787C13.5586 9.01787 13.5594 4.5363 13.5594 4.53077C13.5594 4.23902 13.7955 4.00169 14.0858 4.00169C14.3954 4.00169 14.6117 4.21923 14.6117 4.53077L14.6116 9.1585C14.6116 9.41739 14.8214 9.62725 15.0803 9.62725C15.3392 9.62725 15.5491 9.41739 15.5491 9.1585C15.5491 9.1585 15.5492 5.86773 15.5492 5.84359C15.5492 5.55184 15.7851 5.31452 16.0752 5.31452H16.0841C16.3741 5.31452 16.61 5.55184 16.61 5.84359V10.7402C16.5528 13.2973 14.6555 15.5905 11.9798 15.5905C9.36546 15.5896 7.34928 13.3261 7.34928 10.7817V7.73627C7.61103 7.81352 7.85107 7.95569 8.04954 8.15462C8.30107 8.40686 8.53015 9.11251 8.53404 9.64248V10.7503C8.53404 10.9835 8.70317 11.181 8.93412 11.214C8.98432 11.2217 10.1696 11.4158 10.7034 12.5455C10.7835 12.7149 10.9519 12.8141 11.1275 12.8141C11.1946 12.8141 11.2628 12.7996 11.3275 12.769C11.5616 12.6585 11.6616 12.379 11.5511 12.145C11.0166 11.0139 10.0268 10.5572 9.47154 10.3838L9.47173 4.54637C9.47173 4.24736 9.71379 4.00408 10.0113 4.00408C10.4095 4.00408 10.5508 4.29616 10.5508 4.54633Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M14.0444 17.6445C13.7855 17.6445 13.5757 17.8544 13.5757 18.1133V21.1865C13.5757 21.6615 14.042 21.6582 14.3669 21.6582C14.5327 21.6582 14.7728 21.6573 15.1241 21.6553C15.3829 21.6538 15.5917 21.4428 15.5902 21.1839C15.5888 20.9259 15.3792 20.7178 15.1215 20.7178C15.1206 20.7178 15.1197 20.7178 15.1189 20.7178C14.913 20.719 14.6961 20.7198 14.5132 20.7202V18.1133C14.5132 17.8544 14.3033 17.6445 14.0444 17.6445Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M7.48963 21.6579C7.74852 21.6579 7.95838 21.448 7.95838 21.1891V18.1133C7.95838 17.8544 7.74852 17.6445 7.48963 17.6445C7.23074 17.6445 7.02088 17.8544 7.02088 18.1133V19.1297H5.82983V18.1133C5.82983 17.8544 5.61997 17.6445 5.36108 17.6445C5.10219 17.6445 4.89233 17.8544 4.89233 18.1133V21.1891C4.89233 21.448 5.10219 21.6579 5.36108 21.6579C5.61997 21.6579 5.82983 21.448 5.82983 21.1891V20.0672H7.02088V21.1891C7.02088 21.448 7.23074 21.6579 7.48963 21.6579Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M16.2369 21.1799C16.2531 21.3965 16.4816 21.6138 16.7056 21.6138C16.7312 21.6137 17.3346 21.6113 17.5761 21.6071C18.5271 21.5905 19.2173 20.7685 19.2173 19.6526C19.2173 18.4795 18.5445 17.6914 17.5431 17.6914H16.6978C16.436 17.6914 16.229 17.9032 16.229 18.1632C16.2291 18.1632 16.232 21.1528 16.2369 21.1799ZM17.5431 18.6289C18.2267 18.6289 18.2798 19.4124 18.2798 19.6526C18.2798 20.1526 18.0571 20.6611 17.5597 20.6697C17.4683 20.6713 17.3194 20.6727 17.171 20.6737C17.1699 20.3296 17.168 18.9877 17.1673 18.6289H17.5431Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M8.76245 19.6512C8.76245 20.7577 9.66264 21.6579 10.7691 21.6579C11.8756 21.6579 12.7758 20.7577 12.7758 19.6512C12.7758 18.5447 11.8756 17.6445 10.7691 17.6445C9.66264 17.6445 8.76245 18.5447 8.76245 19.6512ZM11.8383 19.6512C11.8383 20.2407 11.3587 20.7204 10.7691 20.7204C10.1796 20.7204 9.69995 20.2407 9.69995 19.6512C9.69995 19.0617 10.1796 18.582 10.7691 18.582C11.3587 18.582 11.8383 19.0617 11.8383 19.6512Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n      <path\n        d=\"M11.6406 0.647517C11.7161 0.831736 11.9093 0.951173 12.108 0.935705C12.3077 0.920142 12.4789 0.776611 12.528 0.582267C12.6287 0.183783 12.1825 -0.141202 11.8327 0.0667358C11.6361 0.183548 11.5527 0.436392 11.6406 0.647517Z\"\n        fill={PrimaryColor ? \"#436EB6\" : \"#CB9932\"}\n      />\n    </svg>\n  );\n}\n\nexport default HoldIcon;\n"], "names": [], "mappings": ";;;;;AAOA,SAAS,SAAS,EAAE,SAAS,EAAE,YAAY,EAAiB;IAC1D,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,8OAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,8OAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,8OAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,8OAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,8OAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;0BAEnC,8OAAC;gBACC,GAAE;gBACF,MAAM,eAAe,YAAY;;;;;;;;;;;;AAIzC;uCAEe", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/DeleteIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction DeleteIcon({ className }: { className?: string }) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className={className} width=\"25\" height=\"28\" viewBox=\"0 0 25 28\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.3291 27.3361C10.5225 27.3361 8.76112 27.3161 7.01846 27.2801C4.78912 27.2361 3.24646 25.7908 2.99446 23.5081C2.57446 19.7215 1.85579 10.7961 1.84912 10.7068C1.80379 10.1561 2.21446 9.67347 2.76512 9.62947C3.30779 9.6148 3.79846 9.99614 3.84246 10.5455C3.84912 10.6361 4.56646 19.5308 4.98246 23.2881C5.12512 24.5855 5.82512 25.2548 7.05979 25.2801C10.3931 25.3508 13.7945 25.3548 17.4611 25.2881C18.7731 25.2628 19.4825 24.6068 19.6291 23.2788C20.0425 19.5535 20.7625 10.6361 20.7705 10.5455C20.8145 9.99614 21.3011 9.61214 21.8465 9.62947C22.3971 9.6748 22.8078 10.1561 22.7638 10.7068C22.7558 10.7975 22.0331 19.7455 21.6171 23.4988C21.3585 25.8281 19.8198 27.2455 17.4971 27.2881C15.7198 27.3188 14.0051 27.3361 12.3291 27.3361Z\"\n        fill=\"#D00000\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M23.6107 7.32031H1C0.448 7.32031 0 6.87231 0 6.32031C0 5.76831 0.448 5.32031 1 5.32031H23.6107C24.1627 5.32031 24.6107 5.76831 24.6107 6.32031C24.6107 6.87231 24.1627 7.32031 23.6107 7.32031Z\"\n        fill=\"#D00000\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M19.2538 7.31997C17.7364 7.31997 16.4191 6.23864 16.1204 4.75064L15.7964 3.1293C15.7284 2.88264 15.4471 2.66797 15.1271 2.66797H9.48311C9.16311 2.66797 8.88178 2.88264 8.80045 3.19064L8.48978 4.75064C8.19245 6.23864 6.87378 7.31997 5.35645 7.31997C4.80445 7.31997 4.35645 6.87197 4.35645 6.31997C4.35645 5.76797 4.80445 5.31997 5.35645 5.31997C5.92445 5.31997 6.41778 4.91464 6.52978 4.3573L6.85378 2.73597C7.18311 1.4933 8.25911 0.667969 9.48311 0.667969H15.1271C16.3511 0.667969 17.4271 1.4933 17.7431 2.67597L18.0818 4.3573C18.1924 4.91464 18.6858 5.31997 19.2538 5.31997C19.8058 5.31997 20.2538 5.76797 20.2538 6.31997C20.2538 6.87197 19.8058 7.31997 19.2538 7.31997Z\"\n        fill=\"#D00000\"\n      />\n    </svg>\n  );\n}\n\nexport default DeleteIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAW;QAAW,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;;0BAC5G,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/InfoIcon.tsx"], "sourcesContent": ["import React, { useId } from \"react\";\nimport { Tooltip } from \"react-tooltip\";\n\ninterface InfoIconProps {\n  tooltip: React.ReactNode;\n  id?: string;\n  place?: \"top\" | \"bottom\" | \"left\" | \"right\";\n  className?: string;\n}\n\nfunction InfoIcon({ tooltip, id, place = \"bottom\", className }: InfoIconProps) {\n  const generatedId = useId();\n  const anchorId = id || `info-icon-${generatedId}`;\n  return (\n    <>\n      <span id={anchorId} className={className}>\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"21\" height=\"21\" viewBox=\"0 0 23 23\" style={{ cursor: \"pointer\" }} fill=\"none\">\n          <g clipPath=\"url(#clip0_9605_3144)\">\n            <path\n              d=\"M11.5 2.15625C9.65198 2.15625 7.84547 2.70425 6.30889 3.73096C4.77232 4.75766 3.57471 6.21695 2.8675 7.9243C2.1603 9.63165 1.97526 11.5104 2.33579 13.3229C2.69632 15.1354 3.58623 16.8003 4.89298 18.107C6.19972 19.4138 7.86462 20.3037 9.67713 20.6642C11.4896 21.0247 13.3684 20.8397 15.0757 20.1325C16.783 19.4253 18.2423 18.2277 19.269 16.6911C20.2958 15.1545 20.8438 13.348 20.8438 11.5C20.8411 9.02269 19.8559 6.64759 18.1041 4.89586C16.3524 3.14413 13.9773 2.15887 11.5 2.15625ZM11.1406 6.46875C11.3539 6.46875 11.5623 6.53198 11.7396 6.65045C11.9169 6.76891 12.0551 6.93729 12.1367 7.13429C12.2183 7.3313 12.2396 7.54807 12.198 7.75721C12.1564 7.96634 12.0538 8.15845 11.903 8.30922C11.7522 8.46 11.5601 8.56268 11.351 8.60428C11.1418 8.64588 10.925 8.62453 10.728 8.54293C10.531 8.46133 10.3627 8.32315 10.2442 8.14585C10.1257 7.96855 10.0625 7.76011 10.0625 7.54688C10.0625 7.26094 10.1761 6.98671 10.3783 6.78453C10.5805 6.58234 10.8547 6.46875 11.1406 6.46875ZM12.2188 16.5312C11.8375 16.5312 11.4719 16.3798 11.2023 16.1102C10.9327 15.8406 10.7813 15.475 10.7813 15.0938V11.5C10.5906 11.5 10.4078 11.4243 10.273 11.2895C10.1382 11.1547 10.0625 10.9719 10.0625 10.7812C10.0625 10.5906 10.1382 10.4078 10.273 10.273C10.4078 10.1382 10.5906 10.0625 10.7813 10.0625C11.1625 10.0625 11.5281 10.214 11.7977 10.4835C12.0673 10.7531 12.2188 11.1188 12.2188 11.5V15.0938C12.4094 15.0938 12.5922 15.1695 12.727 15.3043C12.8618 15.4391 12.9375 15.6219 12.9375 15.8125C12.9375 16.0031 12.8618 16.1859 12.727 16.3207C12.5922 16.4555 12.4094 16.5312 12.2188 16.5312Z\"\n              fill=\"#436EB6\"\n            />\n          </g>\n          <defs>\n            <clipPath id=\"clip0_9605_3144\">\n              <rect width=\"23\" height=\"23\" fill=\"white\" />\n            </clipPath>\n          </defs>\n        </svg>\n      </span>\n      <Tooltip anchorSelect={`#${anchorId}`} className=\"responsive-tooltip\" place={place}>\n        {tooltip}\n      </Tooltip>\n    </>\n  );\n}\n\nexport default InfoIcon;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,SAAS,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,QAAQ,EAAE,SAAS,EAAiB;IAC3E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACxB,MAAM,WAAW,MAAM,CAAC,UAAU,EAAE,aAAa;IACjD,qBACE;;0BACE,8OAAC;gBAAK,IAAI;gBAAU,WAAW;0BAC7B,cAAA,8OAAC;oBAAI,OAAM;oBAA6B,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,OAAO;wBAAE,QAAQ;oBAAU;oBAAG,MAAK;;sCACpH,8OAAC;4BAAE,UAAS;sCACV,cAAA,8OAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;sCAGT,8OAAC;sCACC,cAAA,8OAAC;gCAAS,IAAG;0CACX,cAAA,8OAAC;oCAAK,OAAM;oCAAK,QAAO;oCAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC,oKAAA,CAAA,UAAO;gBAAC,cAAc,CAAC,CAAC,EAAE,UAAU;gBAAE,WAAU;gBAAqB,OAAO;0BAC1E;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/resume/ManualUploadResume.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nimport { useForm, useFieldArray, Controller, Resolver } from \"react-hook-form\";\nimport { yupResolver } from \"@hookform/resolvers/yup\";\n\nimport { uploadManualCandidate } from \"@/services/screenResumeServices\";\nimport { formSchemaValidation } from \"@/validations/screenResumeValidations\";\n\nimport ROUTES from \"@/constants/routes\";\nimport { GENDER_OPTIONS } from \"@/constants/screenResumeConstant\";\n\nimport { IFormValues } from \"@/interfaces/screenResumeInterfaces\";\n\nimport Loader from \"@/components/loader/Loader\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\nimport HoldIcon from \"@/components/svgComponents/HoldIcon\";\nimport Button from \"@/components/formElements/Button\";\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\nimport DeleteIcon from \"@/components/svgComponents/DeleteIcon\";\nimport InfoIcon from \"@/components/svgComponents/InfoIcon\";\n\nimport { toastMessageSuccess, toastMessageError } from \"@/utils/helper\";\n\nimport style from \"@/styles/commonPage.module.scss\";\nimport { useTranslations } from \"next-intl\";\nimport { PDF_FILE_SIZE_LIMIT, PDF_FILE_TYPE } from \"@/constants/commonConstants\";\n\n/**\n * ManualUploadResume Component\n *\n * Allows hiring managers to manually upload candidate resumes and assessments.\n * Supports adding multiple candidates (up to 5) with validation for required fields.\n *\n * @returns {JSX.Element} The rendered ManualUploadResume component\n */\n\nfunction ManualUploadResume({\n  params,\n  searchParams,\n}: {\n  params: Promise<{ jobId: string }>;\n  searchParams: Promise<{ title: string; jobUniqueId: string }>;\n}) {\n  const router = useRouter();\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n\n  const paramsPromise = React.use(params);\n  const searchParamsPromise = React.use(searchParams);\n  const t = useTranslations();\n  // Initialize form with validation schema\n  const {\n    control,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<IFormValues>({\n    resolver: yupResolver(formSchemaValidation(t)) as unknown as Resolver<IFormValues>,\n    defaultValues: {\n      candidates: [{ name: \"\", email: \"\", gender: \"\", resume: null, assessment: null, additionalInfo: \"\" }],\n    },\n  });\n\n  // Initialize field array for dynamic candidates\n  const { fields, append, remove } = useFieldArray({\n    control,\n    name: \"candidates\",\n  });\n\n  // State for loading status during form submission\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const resumeFileRef = useRef<HTMLInputElement>(null);\n  const assessmentFileRef = useRef<HTMLInputElement>(null);\n  useEffect(() => {\n    if (!Number(paramsPromise.jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {\n      router.push(ROUTES.JOBS.ACTIVE_JOBS);\n    }\n  }, [paramsPromise.jobId, searchParamsPromise?.title]);\n\n  /**\n   * Handles form submission for candidate uploads\n   *\n   * Processes the submitted form data, calls the API to upload candidates,\n   * shows success/error messages, and redirects on success.\n   *\n   * @param {IFormValues} data - The validated form data containing candidate information\n   * @returns {Promise<void>}\n   */\n  const onSubmit = async (data: IFormValues) => {\n    console.log(\"onSubmit=========>\", data);\n    try {\n      setIsSubmitting(true);\n      // Here we would normally get these IDs from auth context or redux store\n      // For now using placeholder values\n      const jobId = Number(paramsPromise.jobId); // Replace with actual job ID\n      // Upload candidate data with resume and assessment files\n      const response = await uploadManualCandidate(data, jobId);\n      if (response && response.data && response.data.success) {\n        toastMessageSuccess(t(\"candidates_uploaded_successfully\"));\n        reset(); // Reset the form after successful submission\n        router.push(\n          `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}` +\n            `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`\n        );\n      } else {\n        toastMessageError(t(response.data?.message) || t(\"something_went_wrong\"));\n      }\n    } catch (error) {\n      console.error(\"Error uploading candidates:\", error);\n      toastMessageError(t(\"something_went_wrong\"));\n      reset();\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  /**\n   * Validates if a file meets PDF requirements\n   * @param file - The file to validate\n   * @returns boolean - True if validation passes, False otherwise\n   */\n  const handleFileChange = (file: File | null): boolean => {\n    if (!file) {\n      return false;\n    }\n    // Validate file name contains PDF_FILE_NAME\n    if (!file.name.includes(\".pdf\")) {\n      console.log(\"file name not contains pdf\");\n      toastMessageError(t(\"unsupported_file_type\"));\n      return false;\n    }\n\n    // Validate file type (PDF only)\n    if (!PDF_FILE_TYPE.includes(file.type)) {\n      toastMessageError(t(\"pdf_only\"));\n      return false;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > PDF_FILE_SIZE_LIMIT) {\n      toastMessageError(t(\"pdf_size\"));\n      return false;\n    }\n    if (file.name.length > 50) {\n      toastMessageError(t(\"pdf_name\"));\n      return false;\n    }\n\n    return true;\n  };\n\n  return (\n    <div ref={scrollContainerRef} className={`${style.resume_page} ${style.manual_upload_resume}`}>\n      <div className=\"container\">\n        <div className={style.inner_page}>\n          <div className=\"common-page-header\">\n            <div className=\"common-page-head-section\">\n              <div className=\"main-heading\">\n                <h2>\n                  <BackArrowIcon onClick={() => router.push(`${ROUTES.JOBS.ACTIVE_JOBS}`)} />\n                  {t(\"manual_upload_resume\")} <span>{searchParamsPromise?.title}</span>\n                </h2>\n                <Button\n                  className=\"clear-btn p-0 color-primary\"\n                  onClick={() =>\n                    router.push(\n                      `${ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION}/${paramsPromise.jobId}` +\n                        `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`\n                    )\n                  }\n                >\n                  <HoldIcon className=\"me-2\" PrimaryColor /> {t(\"view_panding_action\")}\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          <form\n            onSubmit={handleSubmit((data) => {\n              setTimeout(() => {\n                window.scrollTo({ top: 0, behavior: \"smooth\" });\n              }, 50);\n              onSubmit(data);\n            })}\n          >\n            {fields.map((field, index) => (\n              <div key={field.id} className={style.candidate_card}>\n                <div className={`d-flex align-items-center justify-content-between ${style.candidate_card_header}`}>\n                  <h3>\n                    {t(\"candidate\")} {index + 1}\n                  </h3>\n                  {index > 0 && (\n                    <Button type=\"button\" onClick={() => remove(index)} className=\"clear-btn p-0\">\n                      <DeleteIcon className=\"p-1\" />\n                    </Button>\n                  )}\n                </div>\n                <div className={style.candidate_card_body}>\n                  <div className=\"row\">\n                    {/* Name Field */}\n                    <div className=\"col-md-6\">\n                      <InputWrapper>\n                        <InputWrapper.Label htmlFor={`candidates.${index}.name`} required>\n                          {t(\"name\")}\n                          <InfoIcon\n                            tooltip=\"Enter the candidate’s full name.\"\n                            id=\"BasicDetails\"\n                            place=\"bottom\"\n                          />\n                        </InputWrapper.Label>\n                        <Controller\n                          control={control}\n                          name={`candidates.${index}.name`}\n                          render={({ field }) => (\n                            <input\n                              {...field}\n                              id={`candidates.${index}.name`}\n                              type=\"text\"\n                              placeholder=\"Please enter full name of the candidate\"\n                              className=\"form-control\"\n                            />\n                          )}\n                        />\n\n                        {errors.candidates?.[index]?.name && <div className=\"auth-msg error\">{errors.candidates[index]?.name?.message}</div>}\n                      </InputWrapper>\n                    </div>\n\n                    {/* Email Field */}\n                    <div className=\"col-md-6\">\n                      <InputWrapper>\n                        <InputWrapper.Label htmlFor={`candidates.${index}.email`} required>\n                          {t(\"email\")}\n                          <InfoIcon\n                            tooltip=\"Provide a valid email address to contact the candidate regarding job-related updates.\"\n                            id=\"BasicDetails\"\n                            place=\"bottom\"\n                          />\n                        </InputWrapper.Label>\n                        <Controller\n                          control={control}\n                          name={`candidates.${index}.email`}\n                          render={({ field }) => (\n                            <input\n                              {...field}\n                              id={`candidates.${index}.email`}\n                              type=\"email\"\n                              placeholder=\"Please enter candidate’s email address\"\n                              className=\"form-control\"\n                            />\n                          )}\n                        />\n                        {errors.candidates?.[index]?.email && <div className=\"auth-msg error\">{errors.candidates[index]?.email?.message}</div>}\n                      </InputWrapper>\n                    </div>\n\n                    {/* Gender Field */}\n                    <div className=\"col-md-6\">\n                      <InputWrapper>\n                        <InputWrapper.Label htmlFor={`candidates.${index}.gender`} required>\n                          {t(\"gender\")}\n                          <InfoIcon\n                            tooltip=\"Select the candidate’s gender to support inclusive hiring analytics and reporting.\"\n                            id=\"BasicDetails\"\n                            place=\"bottom\"\n                          />\n                        </InputWrapper.Label>\n                        <Controller\n                          control={control}\n                          name={`candidates.${index}.gender`}\n                          render={({ field }) => (\n                            <select {...field} id={`candidates.${index}.gender`} className=\"form-control\">\n                              <option value=\"\" disabled>\n                                {t(\"select_gender\")}\n                              </option>\n                              {GENDER_OPTIONS.map((option) => (\n                                <option key={option.value} value={option.value}>\n                                  {option.label}\n                                </option>\n                              ))}\n                            </select>\n                          )}\n                        />\n                        {errors.candidates?.[index]?.gender && <div className=\"auth-msg error\">{errors.candidates[index]?.gender?.message}</div>}\n                      </InputWrapper>\n                    </div>\n\n                    {/* Resume Upload Field */}\n                    <div className=\"col-md-6\">\n                      <InputWrapper>\n                        <InputWrapper.Label htmlFor={`candidates.${index}.resume`} required>\n                          {t(\"upload_resume\")}\n                          <InfoIcon\n                            tooltip=\"Upload the candidate’s latest resume in PDF or DOC format for evaluation\"\n                            id=\"BasicDetails\"\n                            place=\"bottom\"\n                          />\n                        </InputWrapper.Label>\n                        <div className={style.input_type_file}>\n                          <Controller\n                            name={`candidates.${index}.resume`}\n                            control={control}\n                            render={({ field }) => (\n                              <input\n                                id={`candidates.${index}.resume`}\n                                type=\"file\"\n                                ref={resumeFileRef}\n                                accept=\".pdf\"\n                                onChange={(e) => {\n                                  const file = e.target.files?.[0] || null;\n                                  console.log(\"file==============>\", file);\n                                  if (file) {\n                                    if (handleFileChange(file)) {\n                                      field.onChange(file);\n                                    } else {\n                                      e.target.value = \"\";\n                                      field.onChange(null);\n                                    }\n                                  } else {\n                                    field.onChange(null);\n                                  }\n                                }}\n                              />\n                            )}\n                          />\n                        </div>\n                        {errors.candidates?.[index]?.resume && <div className=\"auth-msg error\">{errors.candidates[index]?.resume?.message}</div>}\n                      </InputWrapper>\n                    </div>\n\n                    {/* Assessment Upload Field */}\n                    <div className=\"col-md-6\">\n                      <InputWrapper>\n                        <InputWrapper.Label htmlFor={`candidates.${index}.assessment`}>\n                          {t(\"upload_assesment\")}\n                          <InfoIcon\n                            tooltip=\"Attach any completed assessments or test results relevant to the job role.\"\n                            id=\"BasicDetails\"\n                            place=\"bottom\"\n                          />\n                        </InputWrapper.Label>\n                        <div className={style.input_type_file}>\n                          <Controller\n                            name={`candidates.${index}.assessment`}\n                            control={control}\n                            render={({ field }) => (\n                              <input\n                                id={`candidates.${index}.assessment`}\n                                type=\"file\"\n                                accept=\".pdf\"\n                                ref={assessmentFileRef}\n                                onChange={(e) => {\n                                  const file = e.target.files?.[0] || null;\n                                  if (file) {\n                                    if (handleFileChange(file)) {\n                                      field.onChange(file);\n                                    } else {\n                                      e.target.value = \"\";\n                                      field.onChange(null);\n                                    }\n                                  } else {\n                                    field.onChange(null);\n                                  }\n                                }}\n                              />\n                            )}\n                          />\n                        </div>\n                        {errors.candidates?.[index]?.assessment && (\n                          <div className=\"auth-msg error\">{errors.candidates[index]?.assessment?.message}</div>\n                        )}\n                      </InputWrapper>\n                    </div>\n\n                    {/* Additional Information Field */}\n                    <div className=\"col-md-12\">\n                      <InputWrapper>\n                        <InputWrapper.Label htmlFor={`candidates.${index}.additionalInfo`}>\n                          {t(\"additional_info\")}\n                          <InfoIcon\n                            tooltip=\"Add any extra details about the candidate like experience, availability, or preferences.\"\n                            id=\"BasicDetails\"\n                            place=\"bottom\"\n                          />\n                        </InputWrapper.Label>\n                        <Controller\n                          control={control}\n                          name={`candidates.${index}.additionalInfo`}\n                          render={({ field }) => (\n                            <textarea\n                              {...field}\n                              id={`candidates.${index}.additionalInfo`}\n                              rows={6}\n                              placeholder={t(\"enter_additional_info_about_candidate\")}\n                              className=\"form-control\"\n                            />\n                          )}\n                        />\n                        {errors.candidates?.[index]?.additionalInfo && (\n                          <div className=\"auth-msg error\">{errors.candidates[index]?.additionalInfo?.message}</div>\n                        )}\n                      </InputWrapper>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n\n            <div className={style.add_another_candidate_link}>\n              <Button\n                type=\"button\"\n                onClick={() => {\n                  /**\n                   * Handles adding another candidate to the form\n                   *\n                   * Checks if maximum limit (5) is reached before adding a new candidate entry.\n                   * Shows a message if the limit is reached.\n                   */\n                  if (fields.length < 5) {\n                    append({ name: \"\", email: \"\", gender: \"\", resume: null, assessment: null, additionalInfo: \"\" });\n                  } else {\n                    toastMessageSuccess(\"Maximum 5 candidates allowed\");\n                  }\n                }}\n                className=\"clear-btn p-0 color-primary\"\n                disabled={fields.length >= 5}\n              >\n                {t(\"add_candidates_resume\")} {fields.length >= 5 ? \"(Maximum 5 candidates allowed)\" : \"\"}\n              </Button>\n            </div>\n\n            <div className=\"button-align py-5\">\n              <Button type=\"submit\" className=\"primary-btn rounded-md minWidth\" disabled={isSubmitting}>\n                {t(\"analyze\")} {isSubmitting && <Loader />}\n              </Button>\n              <Button\n                type=\"button\"\n                onClick={() => {\n                  /**\n                   * Resets the form to its default state\n                   *\n                   * Clears all form fields and errors, returning the form to its initial state\n                   * with a single empty candidate entry.\n                   */\n                  reset();\n                }}\n                className=\"dark-outline-btn rounded-md minWidth\"\n                disabled={isSubmitting}\n              >\n                {t(\"reset\")}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default ManualUploadResume;\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA;;;;;;;CAOC,GAED,SAAS,mBAAmB,EAC1B,MAAM,EACN,YAAY,EAIb;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAChC,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IACtC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD;IACxB,yCAAyC;IACzC,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,6IAAA,CAAA,uBAAoB,AAAD,EAAE;QAC3C,eAAe;YACb,YAAY;gBAAC;oBAAE,MAAM;oBAAI,OAAO;oBAAI,QAAQ;oBAAI,QAAQ;oBAAM,YAAY;oBAAM,gBAAgB;gBAAG;aAAE;QACvG;IACF;IAEA,gDAAgD;IAChD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,kDAAkD;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO,cAAc,KAAK,KAAK,CAAC,qBAAqB,SAAS,qBAAqB,MAAM,WAAW,GAAG;YAC1G,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW;QACrC;IACF,GAAG;QAAC,cAAc,KAAK;QAAE,qBAAqB;KAAM;IAEpD;;;;;;;;GAQC,GACD,MAAM,WAAW,OAAO;QACtB,QAAQ,GAAG,CAAC,sBAAsB;QAClC,IAAI;YACF,gBAAgB;YAChB,wEAAwE;YACxE,mCAAmC;YACnC,MAAM,QAAQ,OAAO,cAAc,KAAK,GAAG,6BAA6B;YACxE,yDAAyD;YACzD,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACtD,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE;gBACtB,SAAS,6CAA6C;gBACtD,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE,GACtE,CAAC,OAAO,EAAE,qBAAqB,MAAM,aAAa,EAAE,qBAAqB,aAAa;YAE5F,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,SAAS,IAAI,EAAE,YAAY,EAAE;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IACA;;;;GAIC,GACD,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QACA,4CAA4C;QAC5C,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;YAC/B,QAAQ,GAAG,CAAC;YACZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QAEA,gCAAgC;QAChC,IAAI,CAAC,mIAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACtC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,mIAAA,CAAA,sBAAmB,EAAE;YACnC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QACA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI;YACzB,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE;YACpB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAoB,WAAW,GAAG,yJAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,EAAE,yJAAA,CAAA,UAAK,CAAC,oBAAoB,EAAE;kBAC3F,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,UAAU;;kCAC9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,oJAAA,CAAA,UAAa;gDAAC,SAAS,IAAM,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,EAAE;;;;;;4CACrE,EAAE;4CAAwB;0DAAC,8OAAC;0DAAM,qBAAqB;;;;;;;;;;;;kDAE1D,8OAAC,4IAAA,CAAA,UAAM;wCACL,WAAU;wCACV,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE,GACtE,CAAC,OAAO,EAAE,qBAAqB,MAAM,aAAa,EAAE,qBAAqB,aAAa;;0DAI5F,8OAAC,+IAAA,CAAA,UAAQ;gDAAC,WAAU;gDAAO,YAAY;;;;;;4CAAG;4CAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAMtD,8OAAC;wBACC,UAAU,aAAa,CAAC;4BACtB,WAAW;gCACT,OAAO,QAAQ,CAAC;oCAAE,KAAK;oCAAG,UAAU;gCAAS;4BAC/C,GAAG;4BACH,SAAS;wBACX;;4BAEC,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oCAAmB,WAAW,yJAAA,CAAA,UAAK,CAAC,cAAc;;sDACjD,8OAAC;4CAAI,WAAW,CAAC,kDAAkD,EAAE,yJAAA,CAAA,UAAK,CAAC,qBAAqB,EAAE;;8DAChG,8OAAC;;wDACE,EAAE;wDAAa;wDAAE,QAAQ;;;;;;;gDAE3B,QAAQ,mBACP,8OAAC,4IAAA,CAAA,UAAM;oDAAC,MAAK;oDAAS,SAAS,IAAM,OAAO;oDAAQ,WAAU;8DAC5D,cAAA,8OAAC,iJAAA,CAAA,UAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAI5B,8OAAC;4CAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,mBAAmB;sDACvC,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;8EACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,KAAK,CAAC;oEAAE,QAAQ;;wEAC9D,EAAE;sFACH,8OAAC,+IAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAG;4EACH,OAAM;;;;;;;;;;;;8EAGV,8OAAC,8JAAA,CAAA,aAAU;oEACT,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,KAAK,CAAC;oEAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;4EACE,GAAG,KAAK;4EACT,IAAI,CAAC,WAAW,EAAE,MAAM,KAAK,CAAC;4EAC9B,MAAK;4EACL,aAAY;4EACZ,WAAU;;;;;;;;;;;gEAKf,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,sBAAQ,8OAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;kEAK1G,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;8EACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC;oEAAE,QAAQ;;wEAC/D,EAAE;sFACH,8OAAC,+IAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAG;4EACH,OAAM;;;;;;;;;;;;8EAGV,8OAAC,8JAAA,CAAA,aAAU;oEACT,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC;oEACjC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;4EACE,GAAG,KAAK;4EACT,IAAI,CAAC,WAAW,EAAE,MAAM,MAAM,CAAC;4EAC/B,MAAK;4EACL,aAAY;4EACZ,WAAU;;;;;;;;;;;gEAIf,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,uBAAS,8OAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;kEAK5G,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;8EACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;oEAAE,QAAQ;;wEAChE,EAAE;sFACH,8OAAC,+IAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAG;4EACH,OAAM;;;;;;;;;;;;8EAGV,8OAAC,8JAAA,CAAA,aAAU;oEACT,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;oEAClC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;4EAAQ,GAAG,KAAK;4EAAE,IAAI,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;4EAAE,WAAU;;8FAC7D,8OAAC;oFAAO,OAAM;oFAAG,QAAQ;8FACtB,EAAE;;;;;;gFAEJ,wIAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,uBACnB,8OAAC;wFAA0B,OAAO,OAAO,KAAK;kGAC3C,OAAO,KAAK;uFADF,OAAO,KAAK;;;;;;;;;;;;;;;;gEAOhC,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,wBAAU,8OAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;;;;;;kEAK9G,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;8EACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;oEAAE,QAAQ;;wEAChE,EAAE;sFACH,8OAAC,+IAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAG;4EACH,OAAM;;;;;;;;;;;;8EAGV,8OAAC;oEAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;8EACnC,cAAA,8OAAC,8JAAA,CAAA,aAAU;wEACT,MAAM,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;wEAClC,SAAS;wEACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gFACC,IAAI,CAAC,WAAW,EAAE,MAAM,OAAO,CAAC;gFAChC,MAAK;gFACL,KAAK;gFACL,QAAO;gFACP,UAAU,CAAC;oFACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;oFACpC,QAAQ,GAAG,CAAC,uBAAuB;oFACnC,IAAI,MAAM;wFACR,IAAI,iBAAiB,OAAO;4FAC1B,MAAM,QAAQ,CAAC;wFACjB,OAAO;4FACL,EAAE,MAAM,CAAC,KAAK,GAAG;4FACjB,MAAM,QAAQ,CAAC;wFACjB;oFACF,OAAO;wFACL,MAAM,QAAQ,CAAC;oFACjB;gFACF;;;;;;;;;;;;;;;;gEAKP,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,wBAAU,8OAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;;;;;;kEAK9G,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;8EACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,WAAW,CAAC;;wEAC1D,EAAE;sFACH,8OAAC,+IAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAG;4EACH,OAAM;;;;;;;;;;;;8EAGV,8OAAC;oEAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,eAAe;8EACnC,cAAA,8OAAC,8JAAA,CAAA,aAAU;wEACT,MAAM,CAAC,WAAW,EAAE,MAAM,WAAW,CAAC;wEACtC,SAAS;wEACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gFACC,IAAI,CAAC,WAAW,EAAE,MAAM,WAAW,CAAC;gFACpC,MAAK;gFACL,QAAO;gFACP,KAAK;gFACL,UAAU,CAAC;oFACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;oFACpC,IAAI,MAAM;wFACR,IAAI,iBAAiB,OAAO;4FAC1B,MAAM,QAAQ,CAAC;wFACjB,OAAO;4FACL,EAAE,MAAM,CAAC,KAAK,GAAG;4FACjB,MAAM,QAAQ,CAAC;wFACjB;oFACF,OAAO;wFACL,MAAM,QAAQ,CAAC;oFACjB;gFACF;;;;;;;;;;;;;;;;gEAKP,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,4BAC3B,8OAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,YAAY;;;;;;;;;;;;;;;;;kEAM7E,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kJAAA,CAAA,UAAY;;8EACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oEAAC,SAAS,CAAC,WAAW,EAAE,MAAM,eAAe,CAAC;;wEAC9D,EAAE;sFACH,8OAAC,+IAAA,CAAA,UAAQ;4EACP,SAAQ;4EACR,IAAG;4EACH,OAAM;;;;;;;;;;;;8EAGV,8OAAC,8JAAA,CAAA,aAAU;oEACT,SAAS;oEACT,MAAM,CAAC,WAAW,EAAE,MAAM,eAAe,CAAC;oEAC1C,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;4EACE,GAAG,KAAK;4EACT,IAAI,CAAC,WAAW,EAAE,MAAM,eAAe,CAAC;4EACxC,MAAM;4EACN,aAAa,EAAE;4EACf,WAAU;;;;;;;;;;;gEAIf,OAAO,UAAU,EAAE,CAAC,MAAM,EAAE,gCAC3B,8OAAC;oEAAI,WAAU;8EAAkB,OAAO,UAAU,CAAC,MAAM,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCArN7E,MAAM,EAAE;;;;;0CA8NpB,8OAAC;gCAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,0BAA0B;0CAC9C,cAAA,8OAAC,4IAAA,CAAA,UAAM;oCACL,MAAK;oCACL,SAAS;wCACP;;;;;mBAKC,GACD,IAAI,OAAO,MAAM,GAAG,GAAG;4CACrB,OAAO;gDAAE,MAAM;gDAAI,OAAO;gDAAI,QAAQ;gDAAI,QAAQ;gDAAM,YAAY;gDAAM,gBAAgB;4CAAG;wCAC/F,OAAO;4CACL,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;wCACtB;oCACF;oCACA,WAAU;oCACV,UAAU,OAAO,MAAM,IAAI;;wCAE1B,EAAE;wCAAyB;wCAAE,OAAO,MAAM,IAAI,IAAI,mCAAmC;;;;;;;;;;;;0CAI1F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4IAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAkC,UAAU;;4CACzE,EAAE;4CAAW;4CAAE,8BAAgB,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;kDAEzC,8OAAC,4IAAA,CAAA,UAAM;wCACL,MAAK;wCACL,SAAS;4CACP;;;;;mBAKC,GACD;wCACF;wCACA,WAAU;wCACV,UAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;uCAEe", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1437, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/manual-upload-resume/%5BjobId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport ManualUploadResume from \"@/components/views/resume/ManualUploadResume\";\nimport React from \"react\";\n\nconst page = ({ params, searchParams }: { params: Promise<{ jobId: string }>; searchParams: Promise<{ title: string; jobUniqueId: string }> }) => {\n  return (\n    <div>\n      <ManualUploadResume params={params} searchParams={searchParams} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAyG;IAC3I,qBACE,8OAAC;kBACC,cAAA,8OAAC,2JAAA,CAAA,UAAkB;YAAC,QAAQ;YAAQ,cAAc;;;;;;;;;;;AAGxD;uCAEe", "debugId": null}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}