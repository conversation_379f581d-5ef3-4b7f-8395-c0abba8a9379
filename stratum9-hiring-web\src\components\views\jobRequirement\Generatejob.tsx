"use client";

// Internal libraries
import { useEffect, useRef, useState } from "react";

import Image from "next/image";
import ReactSelect from "react-select";

import { useTranslations } from "next-intl";
import { useDispatch } from "react-redux";
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, Resolver, SubmitHandler, useForm } from "react-hook-form";
import { redirect, useSearchParams, useRouter } from "next/navigation";

// Components
import Loader from "@/components/loader/Loader";
import Button from "@/components/formElements/Button";
import Checkbox from "@/components/formElements/Checkbox";
import InfoIcon from "@/components/svgComponents/InfoIcon";
import InputWrapper from "@/components/formElements/InputWrapper";
import Select from "@/components/formElements/Select";
import Textbox from "@/components/formElements/Textbox";
import Textarea from "@/components/formElements/Textarea";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";

// Services
import { generateJobSkills } from "@/services/jobRequirements/generateJobServices";
import { processPdfForFormFields } from "@/services/jobRequirements/pdfUploadService";

// Redux, constants, interfaces, types, validations
import { IDepartment, ISkillData } from "@/interfaces/jobRequirementesInterfaces";
import { setJobDetails } from "@/redux/slices/jobDetailsSlice";
import { setSkillsData } from "@/redux/slices/jobSkillsSlice";

import { findDepartments } from "@/services/departmentService";
import { ExtendedFormValues, FormValues } from "@/types/types";

import generateJobValidation, { GenerateJobSchema } from "@/validations/jobRequirementsValidations";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { initialState } from "@/constants/commonConstants";

import {
  CATEGORY_OPTION,
  COMPLIANCE_OPTIONS,
  CURRENCY_SYMBOL,
  FILE_SIZE_LIMIT,
  FILE_TYPE,
  LOCATION_TYPE_OPTIONS,
  SALARY_REMOVE_SYMBOL_REGEX,
  SALARY_CYCLE_OPTIONS,
  TONE_STYLE_OPTIONS,
  HIRING_TYPE,
  EXPERIENCE_LEVEL_OPTIONS,
  HIRING_TYPE_KEY,
  FILE_NAME,
  JOB_GENERATION_UPLOAD_MESSAGES,
} from "@/constants/jobRequirementConstant";

// Assets
import interviewFormIcon from "@/../public/assets/images/interview-form-icon.svg";

// CSS
import "react-tooltip/dist/react-tooltip.css";
import style from "@/styles/commonPage.module.scss";
import ROUTES from "@/constants/routes";
import UploadBox from "@/components/commonComponent/UploadBox";
import AiMarkIcon from "@/components/svgComponents/AiMarkIcon";

function Generatejob() {
  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState("");
  console.log("uploadError===============================>", uploadError);

  // File upload state
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [jdLink, setJdLink] = useState("");
  const [fileToProcess, setFileToProcess] = useState<File | null>(null);
  const [uploadBoxKey, setUploadBoxKey] = useState(0); // Key to force re-render of UploadBox

  // Department Data state
  const [departmentsOptions, setDepartmentsOptions] = useState<IDepartment[]>([]);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  // Refs (useRef) – Lifecycle or DOM Tracking
  const initialFetchDone = useRef(false);

  // React Router hooks
  const searchParams = useSearchParams();
  const hiringType = searchParams?.get(HIRING_TYPE_KEY) || "";

  // Third-party/hooks library hooks
  const t = useTranslations();
  const tGenerate = useTranslations("jobRequirement");
  const router = useRouter();
  const dispatch = useDispatch();

  const [borderDangerFields, setBorderDangerFields] = useState({
    job_title: false,
    state: false,
    city: false,
    job_type: false,
    location_type: false,
    job_description: false,
    responsibilities: false,
    requirements: false,
    education_required: false,
    certifications: false,
    skills_required: false,
    benefits: false,
    tone_style: false,
    compliance_statement: false,
    salary_range: false,
    salary_cycle: false,
    experience_level: false,
    experience_required: false,
    ideal_candidate_traits: false,
    about_company: false,
    additional_info: false,
    department_id: false,
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
    setError,
  } = useForm<FormValues>({
    defaultValues: {
      ...initialState,
    },
    resolver: yupResolver(generateJobValidation(t)) as unknown as Resolver<FormValues>,
    mode: "onSubmit",
  });

  // Helper function to clear file input
  const clearFileInput = () => {
    setSelectedFile(null);
    setUploadBoxKey((prev) => prev + 1); // Force re-render of UploadBox
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setUploadError("");
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }

    if (!file.name.includes(FILE_NAME)) {
      toastMessageError(t("unsupported_file_type"));
      clearFileInput();
      return;
    }
    // Validate file type (PDF only)
    if (!FILE_TYPE.includes(file.type)) {
      toastMessageError(t("pdf_only"));
      clearFileInput();
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > FILE_SIZE_LIMIT) {
      console.log("file size not pdf===============================>", file.size);
      toastMessageError(t("pdf_size"));
      clearFileInput();
      return;
    }

    setSelectedFile(file);
    console.log("file after setSelectedFile===============================>", file);
    // Automatically upload the file after selection
    await handleFileUpload(file);
  };

  const handleFileUpload = async (fileToUpload?: File) => {
    const fileToProcess = fileToUpload || selectedFile;

    if (!fileToProcess) {
      setUploadError(t("pdf_select"));
      return;
    }

    try {
      setIsUploading(true);
      setUploadError("");
      setJdLink("");

      // Process the PDF to extract form fields using GPT
      const response = await processPdfForFormFields(fileToProcess);
      console.log("response===============================>", response);
      if (!response.data) {
        toastMessageError(response.data.message);
        return;
      }

      // Safely access data properties with fallback values
      const responseData = response.data.data || {};
      const formFields = responseData.formFields || {};
      const jd_link = responseData.jd_link ? responseData.jd_link.split("?")?.[0] : "";
      // Store the JD link for later use when saving the job
      setJdLink(jd_link);
      setFileToProcess(fileToProcess);
      clearFileInput(); // Clear the file input after successful processing

      if (!formFields) {
        throw new Error(t("pdf_extract"));
      }

      if (formFields.department_id) {
        setValue("department_id", formFields.department_id);
        setError("department_id", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, department_id: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, department_id: true }));
      }
      // Auto-fill form fields with extracted data based on exact form field names
      // Handle basic text fields
      if (formFields.job_title) {
        setValue("title", formFields.job_title);
        setError("title", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, job_title: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, job_title: true }));
      }

      if (formFields.job_description) {
        setValue("role_overview", formFields.job_description);
        setError("role_overview", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, job_description: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, job_description: true }));
      }
      if (formFields.state) {
        setValue("state", formFields.state);
        setError("state", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, state: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, state: true }));
      }
      if (formFields.city) {
        setValue("city", formFields.city);
        setError("city", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, city: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, city: true }));
      }
      if (formFields.experience_level) {
        setValue("experience_level", formFields.experience_level);
        setError("experience_level", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, experience_level: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, experience_level: true }));
      }

      if (Array.isArray(formFields.compliance_statement) && formFields.compliance_statement.length > 0) {
        // First enable the compliance section
        setValue("show_compliance", true);

        // Validate that the values exist in our options and set the field value to string array
        const validValues = formFields.compliance_statement.filter((value: string) => COMPLIANCE_OPTIONS.some((option) => option.value === value));

        setValue("compliance_statement", validValues);
        setError("compliance_statement", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, compliance_statement: false }));
      } else {
        setValue("compliance_statement", []);
        setError("compliance_statement", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, compliance_statement: true }));
      }

      // Handle select fields (need to match with option values)
      if (formFields.job_type) {
        // Map job_type directly to employment_type since we're using standardized values
        // Convert to appropriate format based on your form's requirements
        if (formFields.job_type.toLowerCase().includes("full")) {
          setValue("employment_type", "full_time");
          setError("employment_type", { type: "manual", message: "" });
        } else if (formFields.job_type.toLowerCase().includes("part")) {
          setValue("employment_type", "part_time");
          setError("employment_type", { type: "manual", message: "" });
        } else if (formFields.job_type.toLowerCase().includes("contract")) {
          setValue("employment_type", "contract");
          setError("employment_type", { type: "manual", message: "" });
        } else if (formFields.job_type.toLowerCase().includes("intern")) {
          setValue("employment_type", "internship");
          setError("employment_type", { type: "manual", message: "" });
        } else if (formFields.job_type.toLowerCase().includes("free")) {
          setValue("employment_type", "freelance");
          setError("employment_type", { type: "manual", message: "" });
        }
        setBorderDangerFields((prev) => ({ ...prev, job_type: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, job_type: true }));
      }
      if (formFields.location_type) {
        const matchedLocationType = LOCATION_TYPE_OPTIONS.find((option) => option.value.toLowerCase() === formFields.location_type?.toLowerCase());
        if (matchedLocationType) {
          setValue("location_type", matchedLocationType.value);
          setError("location_type", { type: "manual", message: "" });
        }
        setBorderDangerFields((prev) => ({ ...prev, location_type: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, location_type: true }));
      }

      if (formFields.salary_cycle) {
        const matchedSalaryCycle = SALARY_CYCLE_OPTIONS.find((option) => option.value.toLowerCase() === formFields.salary_cycle?.toLowerCase());
        if (matchedSalaryCycle) {
          setValue("salary_cycle", matchedSalaryCycle.value);
          setError("salary_cycle", { type: "manual", message: "" });
        }
        setBorderDangerFields((prev) => ({ ...prev, salary_cycle: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, salary_cycle: true }));
      }

      // Handle salary range if present
      if (formFields.salary_range) {
        setValue("salary_range", formFields.salary_range);
        setError("salary_range", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, salary_range: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, salary_range: true }));
      }

      // handle
      if (formFields.responsibilities) {
        setValue("responsibilities", formFields.responsibilities);
        setError("responsibilities", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, responsibilities: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, responsibilities: true }));
      }

      if (formFields.education_required) {
        // Map requirements to educations_requirement
        setValue("educations_requirement", formFields.education_required);
        setError("educations_requirement", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, education_required: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, requirements: true }));
      }

      if (formFields.skills_required) {
        // Map skills_required to skills_and_software_expertise
        setValue("skills_and_software_expertise", formFields.skills_required);
        setError("skills_and_software_expertise", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, skills_required: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, skills_required: true }));
      }

      // Uncomment and adjust the following lines based on your exact form field names for benefits
      if (formFields.benefits) {
        setValue("perks_benefits", formFields.benefits);
        setError("perks_benefits", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, benefits: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, benefits: true }));
      }

      // Handle tone style if present
      if (formFields.tone_style) {
        setValue("tone_style", formFields.tone_style);
        setError("tone_style", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, tone_style: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, tone_style: true }));
      }

      // Store experience data in a variable to use later
      if (formFields.experience_required) {
        setValue("experience_required", formFields.experience_required);
        setError("experience_required", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, experience_required: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, experience_required: true }));
      }

      if (formFields.experience_level) {
        const matchedExperienceLevel = EXPERIENCE_LEVEL_OPTIONS.find(
          (option) => option.value.toLowerCase() === formFields.experience_level?.toLowerCase()
        );
        if (matchedExperienceLevel) {
          setValue("experience_level", matchedExperienceLevel.value);
          setError("experience_level", { type: "manual", message: "" });
        }
        setBorderDangerFields((prev) => ({ ...prev, experience_level: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, experience_level: true }));
      }
      if (formFields.candidate_traits) {
        setValue("ideal_candidate_traits", formFields.candidate_traits);
        setError("ideal_candidate_traits", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, ideal_candidate_traits: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, ideal_candidate_traits: true }));
      }
      if (formFields.about_company) {
        setValue("about_company", formFields.about_company);
        setError("about_company", { type: "manual", message: "" });
        setBorderDangerFields((prev) => ({ ...prev, about_company: false }));
      } else {
        setBorderDangerFields((prev) => ({ ...prev, about_company: true }));
      }
      if (formFields.certifications) {
        setValue("certifications", formFields.certifications);
      }
      if (formFields.additional_info) {
        setValue("additional_info", formFields.additional_info);
      }
    } catch (error: unknown) {
      console.error("Error processing PDF:", error);
      toastMessageError(t("pdf_error"));
      clearFileInput(); // Clear the file input on error
    } finally {
      setIsUploading(false);
    }
  };

  const onSubmit: SubmitHandler<GenerateJobSchema> = async (data) => {
    try {
      setIsSubmitting(true);
      if (jdLink && fileToProcess) {
        const contentType = fileToProcess.type;
        const myHeaders = new Headers({
          "Content-Type": contentType,
        });
        await fetch(jdLink, {
          method: "PUT",
          headers: myHeaders,
          body: fileToProcess,
        });
      }
      const formDataForSubmission: ExtendedFormValues = {
        ...data,
        compliance_statement: (data.compliance_statement || []).filter((item): item is string => typeof item === "string"),
        // Include the jd_link field if it exists
        jd_link: jdLink || undefined,
        hiring_type: hiringType || "",
      };
      // console.log("Form data for submission:", data);
      // return
      const generateJobResult = await generateJobSkills(formDataForSubmission);
      if (generateJobResult && generateJobResult?.data && generateJobResult.data?.success) {
        toastMessageSuccess(t(generateJobResult.data.message as string));

        // Type assertion for the response data
        const responseData = generateJobResult.data?.data as {
          careerSkills: ISkillData[];
          roleSpecificSkills: ISkillData[];
          cultureSpecificSkills: ISkillData[];
        };

        // Dispatch job skills data to Redux store
        dispatch(
          setSkillsData({
            careerSkills: responseData.careerSkills,
            roleSpecificSkills: responseData.roleSpecificSkills,
            cultureSpecificSkills: responseData.cultureSpecificSkills,
          })
        );

        // Dispatch job details to Redux store before API call
        // We store original form data with proper type handling
        dispatch(
          setJobDetails({
            ...formDataForSubmission,
          })
        );

        // Redirect to career skills page
        router.push(ROUTES.JOBS.CAREER_BASED_SKILLS);
      } else {
        toastMessageError(t(generateJobResult.data?.message as string) || t("something_went_wrong"));
      }

      // Reset form if needed or redirect to another page
      setIsSubmitting(false);
    } catch (error) {
      console.error(t("form_submission_error"), error);
      toastMessageError(t("something_went_wrong"));
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (!hiringType || (hiringType !== HIRING_TYPE.EXTERNAL && hiringType !== HIRING_TYPE.INTERNAL)) redirect(ROUTES.DASHBOARD);
  }, [hiringType]);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const response = await findDepartments();
        if (response && response.data && response.data.success) {
          const formattedData = response.data.data.map((item) => ({
            value: item.id,
            label: item.name,
          }));
          setDepartmentsOptions(formattedData);
        }
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    };
    if (initialFetchDone.current) return;
    initialFetchDone.current = true;
    fetchDepartments();
  }, []);

  return (
    <div className={style.job_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon onClick={() => router.push(ROUTES.JOBS.HIRING_TYPE)} />
                Generate Job Requirements <span>Through S9 InnerView</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="job-generate-head">
          <div className="row">
            <div className="col-md-6">
              <div className="job-generate-doc-card">
                <h3 className="sub-tittle mt-0 mb-3">
                  <AiMarkIcon className="me-2" /> Create Job Description With AI
                </h3>
                <p className="mb-4">Effortlessly autofill your interview details by uploading your JD here.</p>
                <InputWrapper className="mb-0">
                  <UploadBox
                    key={uploadBoxKey}
                    UploadBoxClassName="upload-card-sm"
                    onChange={handleFileChange}
                    inputRef={fileInputRef}
                    isLoading={isUploading}
                    uploadingMessages={JOB_GENERATION_UPLOAD_MESSAGES}
                    messageInterval={2000} // Change message every 1.8 seconds
                  />
                </InputWrapper>
              </div>
            </div>
            <div className="col-md-6">
              <Image src={interviewFormIcon} alt="interviewFormIcon" className={style.interview_form_icon} />
            </div>
          </div>
        </div>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="row mt-5">
            <h2 className={style.section_heading}>
              {tGenerate("basic_details")}{" "}
              <InfoIcon tooltip="Basic job title, location, type, and salary details." id="BasicDetails" place="bottom" />
            </h2>
            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="title" required>
                  {tGenerate("job_title")}
                </InputWrapper.Label>
                <Textbox
                  className={`${borderDangerFields.job_title ? "border-danger" : ""} form-control`}
                  control={control}
                  name="title"
                  type="text"
                  placeholder={tGenerate("eneter_job_title")}
                ></Textbox>
                {errors.title && <InputWrapper.Error message={errors.title.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="employment_type" required>
                  {tGenerate("employment_type")}
                </InputWrapper.Label>
                <div className="icon-align">
                  <Select
                    control={control}
                    name="employment_type"
                    options={CATEGORY_OPTION}
                    className={`${borderDangerFields.job_type ? "border-danger" : ""} w-100`}
                    placeholder={tGenerate("select_employment_type")}
                  />
                </div>
                {errors.employment_type && <InputWrapper.Error message={errors.employment_type.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="department_id" required>
                  {tGenerate("depeartment")}
                </InputWrapper.Label>
                <div className="icon-align">
                  <Select
                    control={control}
                    name="department_id"
                    options={departmentsOptions}
                    className={`w-100 ${borderDangerFields.department_id ? "border-danger" : ""}`}
                    placeholder="Select Department"
                  />
                </div>
                {errors.department_id && <InputWrapper.Error message={errors.department_id.message as string} />}
              </InputWrapper>
            </div>

            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="salary_range" required>
                  {tGenerate("salary_range")}
                </InputWrapper.Label>
                <div className="icon-align">
                  <Textbox
                    className={`form-control ${borderDangerFields.salary_range ? "border-danger" : ""}`}
                    control={control}
                    name="salary_range"
                    type="text"
                    placeholder={tGenerate("enter_salary_range")}
                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                      // Format only on blur to avoid cursor position issues
                      const value = e.target.value.trim();

                      if (!value) return;

                      // Remove all $ and space symbols to clean the input
                      const cleanValue = value.replace(SALARY_REMOVE_SYMBOL_REGEX, "");

                      // Split by hyphen to handle ranges
                      const parts = cleanValue.split("-");

                      if (parts.length === 1) {
                        // Single value - format as range with empty second part
                        const cleanPart = parts[0].trim();
                        if (cleanPart) {
                          setValue("salary_range", `${CURRENCY_SYMBOL}${cleanPart} - ${CURRENCY_SYMBOL}`, { shouldValidate: true });
                        }
                      } else if (parts.length === 2) {
                        // Range - format both parts
                        const formattedParts = parts.map((part) => {
                          const cleanPart = part.trim();
                          return cleanPart ? `${CURRENCY_SYMBOL}${cleanPart}` : `${CURRENCY_SYMBOL}`;
                        });
                        setValue("salary_range", formattedParts.join(" - "), { shouldValidate: true });
                      }
                    }}
                  />
                  {errors.salary_range && <InputWrapper.Error message={errors.salary_range.message as string} />}
                </div>
              </InputWrapper>
            </div>
            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="salary_cycle" required>
                  {tGenerate("salsry_cycle")}
                </InputWrapper.Label>
                <div className="icon-align">
                  <Select
                    control={control}
                    name="salary_cycle"
                    options={SALARY_CYCLE_OPTIONS}
                    className={`w-100 ${borderDangerFields.salary_cycle ? "border-danger" : ""}`}
                    placeholder={tGenerate("select_salary_cycle")}
                  />
                  {errors.salary_cycle && <InputWrapper.Error message={errors.salary_cycle.message as string} />}
                </div>
              </InputWrapper>
            </div>
            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="location_type" required>
                  {tGenerate("job_location")}
                </InputWrapper.Label>
                <div className="icon-align">
                  <Select
                    control={control}
                    name="location_type"
                    options={LOCATION_TYPE_OPTIONS}
                    className={`${borderDangerFields.location_type ? "border-danger" : ""} w-100`}
                    placeholder={tGenerate("select_location_type")}
                  />
                </div>
                {errors.location_type && <InputWrapper.Error message={errors.location_type.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="email" required>
                  {tGenerate("state")}
                </InputWrapper.Label>
                <Textbox
                  className={`${borderDangerFields.state ? "border-danger" : ""} form-control`}
                  control={control}
                  name="state"
                  type="text"
                  placeholder={tGenerate("enter_state")}
                ></Textbox>
                {errors.state && <InputWrapper.Error message={errors.state.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-6">
              <InputWrapper>
                <InputWrapper.Label htmlFor="email" required>
                  {tGenerate("city")}
                </InputWrapper.Label>
                <Textbox
                  className={`${borderDangerFields.city ? "border-danger" : ""} form-control`}
                  control={control}
                  name="city"
                  type="text"
                  placeholder={tGenerate("enter_city")}
                ></Textbox>
                {errors.city && <InputWrapper.Error message={errors.city.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("role_overview")}{" "}
                <InfoIcon
                  tooltip="Role summary of job purpose and core impact.

"
                  id="RoleOverview"
                  place="bottom"
                />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="role_overview" required>
                  {tGenerate("overview")}
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="role_overview"
                  control={control}
                  placeholder={tGenerate("overview_placeholder")}
                  className={`${borderDangerFields.job_description ? "border-danger" : ""} form-control`}
                />
                {errors.role_overview && <InputWrapper.Error message={errors.role_overview.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("experince_level_heading")} <InfoIcon tooltip="Required experience level." id="ExperienceLevel" place="bottom" />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="experience_level" required>
                  {tGenerate("experince_level")}
                </InputWrapper.Label>
                <Select
                  control={control}
                  name="experience_level"
                  options={EXPERIENCE_LEVEL_OPTIONS}
                  className={`w-100 ${borderDangerFields.experience_level ? "border-danger" : ""}`}
                  placeholder={tGenerate("select_experience_level")}
                />
                {errors.experience_level && <InputWrapper.Error message={errors.experience_level.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("key_responsibilities_heading")}{" "}
                <InfoIcon tooltip="Key tasks and duties expected in the role." id="KeyResponsibilities" place="bottom" />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="name" required>
                  {tGenerate("key_responsibilities")}
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="responsibilities"
                  control={control}
                  placeholder={tGenerate("key_responsibilities_placeholder")}
                  className={`${borderDangerFields.responsibilities ? "border-danger" : ""} form-control`}
                />
                {errors.responsibilities && <InputWrapper.Error message={errors.responsibilities.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("required_skills_heading")}{" "}
                <InfoIcon
                  tooltip="Required skills & essential skills, degrees, and certifications."
                  id="RequiredSkillsQualifications"
                  place="bottom"
                />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="educations_requirement" required>
                  {tGenerate("educational_requirments")}
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="educations_requirement"
                  control={control}
                  placeholder={tGenerate("educational_requirments_placeholder")}
                  className={`${borderDangerFields.requirements ? "border-danger" : ""} form-control`}
                />
                {errors.educations_requirement && <InputWrapper.Error message={errors.educations_requirement.message as string} />}
              </InputWrapper>
              <InputWrapper>
                <InputWrapper.Label htmlFor="certifications">
                  {tGenerate("certifications")} <span className="text-muted">{tGenerate("optional")}</span>
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="certifications"
                  control={control}
                  placeholder={tGenerate("certifications_placeholder")}
                  className="form-control"
                />
                {errors.certifications && <InputWrapper.Error message={errors.certifications.message as string} />}
              </InputWrapper>
              <InputWrapper>
                <InputWrapper.Label htmlFor="skills_and_software_expertise" required>
                  {tGenerate("specfic_skills_or_software_knowledge")}
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="skills_and_software_expertise"
                  control={control}
                  placeholder={tGenerate("specfic_skills_or_software_knowledge_placeholder")}
                  className={`${borderDangerFields.skills_required ? "border-danger" : ""} form-control`}
                />
                {errors.skills_and_software_expertise && <InputWrapper.Error message={errors.skills_and_software_expertise.message as string} />}
              </InputWrapper>
              <InputWrapper>
                <InputWrapper.Label htmlFor="experience_required" required>
                  {tGenerate("years_of_experince")}
                </InputWrapper.Label>
                <Textbox
                  className={`form-control w-50 ${borderDangerFields.experience_required ? "border-danger" : ""}`}
                  control={control}
                  name="experience_required"
                  type="text"
                  placeholder={tGenerate("years_of_experince_placeholder")}
                ></Textbox>
                {errors.experience_required && <InputWrapper.Error message={errors.experience_required.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("ideal_traits_heading")}{" "}
                <InfoIcon tooltip="Ideal personality and workstyle traits preferred." id="IdealCandidateTraits" place="bottom" />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="ideal_candidate_traits" required>
                  {tGenerate("ideal_traits")}
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="ideal_candidate_traits"
                  control={control}
                  placeholder={tGenerate("ideal_traits_placeholder")}
                  className={`form-control ${borderDangerFields.ideal_candidate_traits ? "border-danger" : ""}`}
                />
                {errors.ideal_candidate_traits && <InputWrapper.Error message={errors.ideal_candidate_traits.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("about_company_heading")}{" "}
                <InfoIcon tooltip="About your brief description of company background." id="AboutYourCompany" place="bottom" />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="about_company" required>
                  {tGenerate("about_company")}
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="about_company"
                  control={control}
                  placeholder={tGenerate("about_company_placeholder")}
                  className={`form-control ${borderDangerFields.about_company ? "border-danger" : ""}`}
                />
                {errors.about_company && <InputWrapper.Error message={errors.about_company.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("job_benifits_heading")}{" "}
                <InfoIcon tooltip="Perks & benefits, insurances, and time-off offered." id="PerksBenefits" place="bottom" />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="perks_benefits">
                  {tGenerate("job_benifits")}
                  <span className="text-muted"> {tGenerate("optional")}</span>
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="perks_benefits"
                  control={control}
                  placeholder={tGenerate("job_benifits_placeholder")}
                  className={"form-control"}
                />
                {errors.perks_benefits && <InputWrapper.Error message={errors.perks_benefits.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-6 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("tone_and_style_heading")}{" "}
                <InfoIcon tooltip="Tone & writing tone used in the job description." id="ToneStyle" place="bottom" />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="tone_style" required>
                  {tGenerate("tone_and_style")}
                </InputWrapper.Label>
                <div className="icon-align">
                  <Select
                    control={control}
                    name="tone_style"
                    options={TONE_STYLE_OPTIONS}
                    className={`${borderDangerFields.tone_style ? "border-danger" : ""} w-100`}
                    placeholder={tGenerate("tone_and_style_placeholder")}
                  />
                </div>
                {errors.tone_style && <InputWrapper.Error message={errors.tone_style.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("additional_info_heading")}{" "}
                <InfoIcon tooltip="Extra info like growth or team culture." id="AdditionalInfo" place="bottom" />
              </h2>
              <InputWrapper>
                <InputWrapper.Label htmlFor="additional_info">
                  {t("additional_info")} <span className="text-muted"> {tGenerate("optional")}</span>
                </InputWrapper.Label>
                <Textarea
                  rows={4}
                  name="additional_info"
                  control={control}
                  placeholder={tGenerate("additional_info_placeholder")}
                  className="form-control"
                />
                {errors.additional_info && <InputWrapper.Error message={errors.additional_info.message as string} />}
              </InputWrapper>
            </div>
            <div className="col-md-12 mt-4">
              <h2 className={style.section_heading}>
                {tGenerate("compliance_statement_heading")}{" "}
                <InfoIcon tooltip="Legal policy and compliance statements." id="ComplianceStatements" place="bottom" />{" "}
              </h2>
              <InputWrapper>
                <div className="mb-2">
                  <Checkbox
                    control={control}
                    name="show_compliance"
                    label={
                      <>
                        {" "}
                        {tGenerate("compliance_statement_part1")} <br /> {tGenerate("compliance_statement_part2")}
                      </>
                    }
                  />
                  {errors.show_compliance && <InputWrapper.Error message={errors.show_compliance.message as string} />}
                </div>
                <div className="d-flex align-items-center justify-content-between mb-2">
                  <InputWrapper.Label htmlFor="compliance_statement" required>
                    {tGenerate("compliance_statement_placeholder")}
                  </InputWrapper.Label>
                  <a
                    href={"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf"}
                    className="fs-5 color-primary ms-3 text-decoration-underline"
                    target="_blank"
                  >
                    Learn More
                  </a>
                </div>
                <div className="icon-align mb-3">
                  <Controller
                    name="compliance_statement"
                    control={control}
                    render={({ field }) => (
                      <ReactSelect
                        {...field}
                        isMulti
                        options={COMPLIANCE_OPTIONS}
                        className={`w-100 form-control react-multi-select p-0 ${borderDangerFields.compliance_statement ? "border-danger" : ""} ${watch("show_compliance") ? "show-active" : ""}`}
                        classNamePrefix="select"
                        placeholder={tGenerate("compliance_statement_placeholder")}
                        isDisabled={!watch("show_compliance")}
                        onChange={(selectedOptions) => {
                          field.onChange(selectedOptions ? selectedOptions.map((option) => option.value) : []);
                        }}
                        value={COMPLIANCE_OPTIONS.filter((option) => field.value?.includes(option.value))}
                      />
                    )}
                  />
                  {errors.compliance_statement && <InputWrapper.Error message={errors.compliance_statement.message as string} />}
                </div>
              </InputWrapper>
            </div>

            <div className="col-12">
              <div className="button-align mt-4 pb-5">
                <Button type="submit" className="primary-btn rounded-md" disabled={isSubmitting || isUploading}>
                  {t("submit")}
                  {isSubmitting && <Loader />}
                </Button>
                <Button
                  type="button"
                  className="dark-outline-btn rounded-md"
                  onClick={() => {
                    reset();
                  }}
                  disabled={isSubmitting || isUploading}
                >
                  {t("cancel")}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Generatejob;
