{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ThreeDotsIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\nfunction ThreeDotsIcon() {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M12.0001 19.2C13.3256 19.2 14.4001 20.2745 14.4001 21.6C14.4001 22.9255 13.3256 24 12.0001 24C10.6746 24 9.6001 22.9255 9.6001 21.6C9.6001 20.2745 10.6746 19.2 12.0001 19.2ZM12.0001 9.60005C13.3256 9.60005 14.4001 10.6746 14.4001 12C14.4001 13.3255 13.3256 14.4 12.0001 14.4C10.6746 14.4 9.6001 13.3255 9.6001 12C9.6001 10.6746 10.6746 9.60005 12.0001 9.60005ZM12.0001 0C13.3256 0 14.4001 1.07452 14.4001 2.39999C14.4001 3.72546 13.3256 4.79998 12.0001 4.79998C10.6746 4.79998 9.6001 3.72546 9.6001 2.39999C9.6001 1.07452 10.6746 0 12.0001 0Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default ThreeDotsIcon;\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;kBACtF,cAAA,8OAAC;YACC,UAAS;YACT,UAAS;YACT,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/ModalCloseIcon.tsx"], "sourcesContent": ["const ModalCloseIcon = (props: { className?: string }) => {\n  const { className } = props;\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"40\" height=\"41\" viewBox=\"0 0 40 41\" fill=\"none\" className={className}>\n      <circle cx=\"20.0003\" cy=\"20.5\" r=\"18.209\" fill=\"white\" />\n      <path\n        d=\"M19.9997 2.16602C16.3737 2.16602 12.8292 3.24125 9.81427 5.25574C6.79937 7.27023 4.44954 10.1335 3.06193 13.4835C1.67433 16.8335 1.31126 20.5197 2.01866 24.076C2.72606 27.6323 4.47214 30.899 7.0361 33.463C9.60006 36.0269 12.8668 37.773 16.4231 38.4804C19.9794 39.1878 23.6656 38.8248 27.0156 37.4371C30.3656 36.0495 33.2288 33.6997 35.2433 30.6848C37.2578 27.6699 38.3331 24.1253 38.3331 20.4994C38.3273 15.6388 36.3939 10.979 32.957 7.54206C29.5201 4.10513 24.8603 2.17175 19.9997 2.16602ZM27.0697 25.2144C27.2289 25.3681 27.3559 25.552 27.4432 25.7553C27.5306 25.9587 27.5766 26.1774 27.5785 26.3987C27.5804 26.62 27.5382 26.8395 27.4544 27.0443C27.3706 27.2491 27.2469 27.4352 27.0904 27.5917C26.9339 27.7482 26.7478 27.8719 26.543 27.9557C26.3382 28.0395 26.1187 28.0817 25.8974 28.0798C25.6761 28.0778 25.4574 28.0319 25.2541 27.9445C25.0507 27.8572 24.8668 27.7302 24.7131 27.571L19.9997 22.856L15.2864 27.571C14.9721 27.8746 14.5511 28.0426 14.1141 28.0388C13.6771 28.035 13.259 27.8597 12.95 27.5507C12.641 27.2417 12.4657 26.8237 12.4619 26.3867C12.4581 25.9497 12.6261 25.5287 12.9297 25.2144L17.6431 20.4994L12.9297 15.7844C12.7705 15.6306 12.6436 15.4467 12.5562 15.2434C12.4689 15.04 12.4229 14.8213 12.421 14.6C12.4191 14.3787 12.4612 14.1593 12.545 13.9544C12.6288 13.7496 12.7526 13.5635 12.9091 13.407C13.0656 13.2505 13.2516 13.1268 13.4565 13.043C13.6613 12.9592 13.8808 12.917 14.1021 12.9189C14.3234 12.9209 14.5421 12.9668 14.7454 13.0542C14.9487 13.1415 15.1326 13.2685 15.2864 13.4277L19.9997 18.1427L24.7131 13.4277C24.8668 13.2685 25.0507 13.1415 25.2541 13.0542C25.4574 12.9668 25.6761 12.9209 25.8974 12.9189C26.1187 12.917 26.3382 12.9592 26.543 13.043C26.7478 13.1268 26.9339 13.2505 27.0904 13.407C27.2469 13.5635 27.3706 13.7496 27.4544 13.9544C27.5382 14.1593 27.5804 14.3787 27.5785 14.6C27.5766 14.8213 27.5306 15.04 27.4432 15.2434C27.3559 15.4467 27.2289 15.6306 27.0697 15.7844L22.3564 20.4994L27.0697 25.2144Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n};\n\nexport default ModalCloseIcon;\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,iBAAiB,CAAC;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,qBACE,8OAAC;QAAI,OAAM;QAA6B,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,WAAW;;0BACxG,8OAAC;gBAAO,IAAG;gBAAU,IAAG;gBAAO,GAAE;gBAAS,MAAK;;;;;;0BAC/C,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import { JS<PERSON>, ReactNode } from \"react\";\nimport Button from \"./Button\";\n\n/**\n * Wrapper component for input fields\n * @param {string} className - Class name for the input field\n * @returns {JSX.Element} - Wrapper component\n */\nconst InputWrapper = ({ className, children }: { className?: string; children: ReactNode }): JSX.Element => (\n  <div className={`form-group ${className ?? \"\"}`}>{children}</div>\n);\n\n/**\n * Label component for input fields\n * @param {string} children - Label text\n * @returns {JSX.Element} - Label component\n */\nInputWrapper.Label = function ({\n  children,\n  htmlFor,\n  required,\n  className,\n  onClick,\n  style,\n}: {\n  children: ReactNode;\n  htmlFor?: string;\n  required?: boolean;\n  className?: string;\n  onClick?: () => void;\n  style?: React.CSSProperties;\n  ref?: React.RefObject<HTMLInputElement>;\n}): JSX.Element {\n  return (\n    <label htmlFor={htmlFor} className={className} onClick={onClick} style={style}>\n      {children}\n      {required ? <sup>*</sup> : null}\n    </label>\n  );\n};\n\n/**\n * Error component for input fields to display error message\n * @param { string } message - Error message\n * @param { React.CSSProperties } style - Optional style object\n * @returns { JSX.Element } - Error component\n */\nInputWrapper.Error = function ({ message, style }: { message: string; style?: React.CSSProperties }): JSX.Element | null {\n  return message ? (\n    <p className=\"auth-msg error\" style={style}>\n      {message}\n    </p>\n  ) : null;\n};\n\n/**\n * Icon component for input fields\n * @param { string } src - Icon source\n * @param { function } onClick - Function to be called on click\n * @returns { JSX.Element } - Icon component\n */\nInputWrapper.Icon = function ({\n  children,\n  // src,\n  onClick,\n}: {\n  children: ReactNode;\n  // src: string;\n  onClick?: () => void;\n}): JSX.Element {\n  return (\n    <Button className=\"show-icon\" type=\"button\" onClick={onClick}>\n      {children}\n    </Button>\n  );\n};\n\nexport default InputWrapper;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,QAAQ,EAA+C,iBACxF,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGpD;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC7B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EASN;IACC,qBACE,8OAAC;QAAM,SAAS;QAAS,WAAW;QAAW,SAAS;QAAS,OAAO;;YACrE;YACA,yBAAW,8OAAC;0BAAI;;;;;uBAAU;;;;;;;AAGjC;AAEA;;;;;CAKC,GACD,aAAa,KAAK,GAAG,SAAU,EAAE,OAAO,EAAE,KAAK,EAAoD;IACjG,OAAO,wBACL,8OAAC;QAAE,WAAU;QAAiB,OAAO;kBAClC;;;;;eAED;AACN;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC5B,QAAQ,EACR,OAAO;AACP,OAAO,EAKR;IACC,qBACE,8OAAC,4IAAA,CAAA,UAAM;QAAC,WAAU;QAAY,MAAK;QAAS,SAAS;kBAClD;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/formElements/Textarea.tsx"], "sourcesContent": ["import { TextareaHTMLAttributes } from \"react\";\nimport { Control, Controller, FieldValues, Path } from \"react-hook-form\";\n\ninterface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {\n  name: Path<T>;\n  control: Control<T>;\n}\n\nexport default function Textarea<T extends FieldValues>({ control, name, ...props }: TextareaProps<T>) {\n  return (\n    <Controller\n      control={control}\n      render={({ field }) => <textarea {...props} value={field.value} onChange={field.onChange} aria-label=\"\" />}\n      name={name}\n      defaultValue={\"\" as T[typeof name]}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAgC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAyB;IACnG,qBACE,8OAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAAK,8OAAC;gBAAU,GAAG,KAAK;gBAAE,OAAO,MAAM,KAAK;gBAAE,UAAU,MAAM,QAAQ;gBAAE,cAAW;;;;;;QACrG,MAAM;QACN,cAAc;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/screenResumeServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport { http } from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { IFormValues } from \"@/interfaces/screenResumeInterfaces\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\n// Define interface for pagination parameters\ninterface PaginationParams {\n  limit?: number;\n  offset?: number;\n  job_id?: number;\n  status?: string;\n  hiring_manager_id?: number;\n  organization_id?: number;\n}\n\n// Define interfaces for API responses\ninterface ManualUploadResponse {\n  success: boolean;\n  message: string;\n  data: {\n    name: string;\n    email: string;\n    gender: string;\n    additional_details: string;\n    resume_file: string;\n    resume_text: string;\n    assessment_file: string;\n    assessment_text: string;\n  };\n}\n\ninterface JobApplication {\n  application_id: number;\n  job_id: number;\n  hiring_manager_id: number;\n  candidate_id: number;\n  candidate_name: string;\n  ai_decision: string;\n  ai_reason: string;\n  status?: string;\n  created_ts: string;\n}\n\ninterface JobApplicationResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication[];\n  pagination: {\n    limit: number;\n    offset: number;\n    totalCount: number;\n    hasMore: boolean;\n  };\n}\n\ninterface ChangeApplicationStatusParams {\n  job_id: number;\n  candidate_id: number;\n  hiring_manager_id: number;\n  status: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];\n  hiring_manager_reason: string;\n}\n\ninterface ChangeApplicationStatusResponse {\n  success: boolean;\n  message: string;\n  data: JobApplication;\n}\n\n/**\n * Upload resume and assessment files and get presigned URLs\n * @param file - The file to upload (resume or assessment)\n * @returns Promise with presigned URL response\n */\nexport const getPresignedUrl = async (file: File): Promise<ApiResponse> => {\n  const formData = new FormData();\n  formData.append(\"file\", file);\n  formData.append(\"fileType\", file.type);\n  formData.append(\"fileName\", file.name);\n\n  return http.post(endpoint.resumeScreen.GET_PRESIGNED_URL, formData, {\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n};\n\n/**\n * Upload file to S3 using presigned URL\n * @param presignedUrl - The presigned URL for S3 upload\n * @param file - The file to upload\n * @returns Promise with upload response\n */\nexport const uploadToS3 = async (presignedUrl: string, file: File): Promise<Response> => {\n  return fetch(presignedUrl, {\n    method: \"PUT\",\n    body: file,\n    headers: {\n      \"Content-Type\": file.type,\n    },\n  });\n};\n\n/**\n * Process the file upload to get presigned URL and upload to S3\n * @param file - The file to upload\n * @returns Object with file URL and parsed text\n */\nexport const processFileUpload = async (file: File): Promise<{ fileUrl: string; fileText: string; presignedUrl: string }> => {\n  try {\n    // Get presigned URL\n    const presignedUrlResponse = await getPresignedUrl(file);\n\n    if (!presignedUrlResponse.data) {\n      throw new Error(\"Failed to get presigned URL\");\n    }\n    const responseData = presignedUrlResponse.data;\n\n    // The response might have data nested inside another data property\n    const urlData = responseData.data;\n\n    if (!urlData.presignedUrl || !urlData.fileUrl) {\n      console.error(\"Missing URL information in response:\", urlData);\n      throw new Error(\"Missing URL information in response\");\n    }\n\n    const { presignedUrl, fileUrl, fileText } = urlData;\n\n    // Upload file to S3\n    const uploadResponse = await uploadToS3(presignedUrl, file);\n    if (!uploadResponse.ok) {\n      throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);\n    }\n    // Return the file URL and flag for backend extraction\n    return {\n      fileUrl,\n      fileText: fileText, // Special flag to indicate backend should extract text\n      presignedUrl,\n    };\n  } catch (error) {\n    console.error(\"Error processing file upload:\", error);\n    // Include error details in the console for debugging\n    if (error instanceof Error) {\n      console.error(\"Error message:\", error.message);\n      console.error(\"Error stack:\", error.stack);\n    }\n    throw error;\n  }\n};\n\n/**\n * Upload manual candidate data with resume and assessment\n * @param data - The form values with candidate information\n * @returns Promise with API response\n */\n/**\n * Get all job applications with pagination (not just pending)\n * @param params - Pagination parameters (limit, offset, filters)\n * @returns Promise with job applications response\n */\nexport const getAllPendingJobApplications = async (params: PaginationParams): Promise<ApiResponse<JobApplicationResponse>> => {\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    if (params.limit) queryParams.append(\"limit\", params.limit.toString());\n    // Always include offset parameter, even when it's 0\n    queryParams.append(\"offset\", params.offset !== undefined ? params.offset.toString() : \"0\");\n    if (params.job_id) queryParams.append(\"job_id\", params.job_id.toString());\n    if (params.status) queryParams.append(\"status\", params.status);\n    if (params.hiring_manager_id) queryParams.append(\"hiring_manager_id\", params.hiring_manager_id.toString());\n    if (params.organization_id) queryParams.append(\"organization_id\", params.organization_id.toString());\n\n    // Make API request\n    const url = `${endpoint.resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;\n    return http.get(url);\n  } catch (error) {\n    console.error(\"Error fetching job applications:\", error);\n    throw error;\n  }\n};\n\nexport const uploadManualCandidate = async (data: IFormValues, jobId: number): Promise<ApiResponse<ManualUploadResponse>> => {\n  try {\n    // Process candidates with file uploads\n    const processedCandidates = await Promise.all(\n      data.candidates.map(async (candidate) => {\n        // Process resume file\n        const resumeResult = candidate.resume ? await processFileUpload(candidate.resume as File) : { presignedUrl: \"\", fileUrl: \"\", fileText: \"\" };\n        // Process assessment file\n        const assessmentResult = candidate.assessment\n          ? await processFileUpload(candidate.assessment as File)\n          : { presignedUrl: \"\", fileUrl: \"\", fileText: \"\" };\n        return {\n          name: candidate.name,\n          email: candidate.email,\n          gender: candidate.gender,\n          additional_details: candidate.additionalInfo || \"\",\n          resume_file: resumeResult.presignedUrl,\n          resume_text: resumeResult.fileText,\n          assessment_file: assessmentResult.presignedUrl,\n          assessment_text: assessmentResult.fileText,\n        };\n      })\n    );\n\n    // Create payload for API\n    const payload = {\n      job_id: jobId,\n      candidates: processedCandidates,\n    };\n\n    // Send to backend API\n    return http.post(endpoint.resumeScreen.MANUAL_CANDIDATE_UPLOAD, payload);\n  } catch (error) {\n    console.error(\"Error in uploadManualCandidate:\", error);\n    throw error;\n  }\n};\n\n/**\n * Change the status of a job application (Approve, Reject, or Hold)\n * @param params - Parameters containing job_id, candidate_id, hiring_manager_id, and status\n * @param data - Data containing hiring_manager_reason\n * @returns Promise with API response\n */\nexport const changeApplicationStatus = async (data: ChangeApplicationStatusParams): Promise<ApiResponse<ChangeApplicationStatusResponse>> => {\n  return http.post(endpoint.resumeScreen.CHANGE_APPLICATION_STATUS, data);\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AA0EO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,QAAQ;IACxB,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IACrC,SAAS,MAAM,CAAC,YAAY,KAAK,IAAI;IAErC,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE,UAAU;QAClE,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAQO,MAAM,aAAa,OAAO,cAAsB;IACrD,OAAO,MAAM,cAAc;QACzB,QAAQ;QACR,MAAM;QACN,SAAS;YACP,gBAAgB,KAAK,IAAI;QAC3B;IACF;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,oBAAoB;QACpB,MAAM,uBAAuB,MAAM,gBAAgB;QAEnD,IAAI,CAAC,qBAAqB,IAAI,EAAE;YAC9B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,eAAe,qBAAqB,IAAI;QAE9C,mEAAmE;QACnE,MAAM,UAAU,aAAa,IAAI;QAEjC,IAAI,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,OAAO,EAAE;YAC7C,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;QAE5C,oBAAoB;QACpB,MAAM,iBAAiB,MAAM,WAAW,cAAc;QACtD,IAAI,CAAC,eAAe,EAAE,EAAE;YACtB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,eAAe,MAAM,EAAE;QACzE;QACA,sDAAsD;QACtD,OAAO;YACL;YACA,UAAU;YACV;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,qDAAqD;QACrD,IAAI,iBAAiB,OAAO;YAC1B,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;YAC7C,QAAQ,KAAK,CAAC,gBAAgB,MAAM,KAAK;QAC3C;QACA,MAAM;IACR;AACF;AAYO,MAAM,+BAA+B,OAAO;IACjD,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACnE,oDAAoD;QACpD,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,KAAK,YAAY,OAAO,MAAM,CAAC,QAAQ,KAAK;QACtF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM,CAAC,QAAQ;QACtE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7D,IAAI,OAAO,iBAAiB,EAAE,YAAY,MAAM,CAAC,qBAAqB,OAAO,iBAAiB,CAAC,QAAQ;QACvG,IAAI,OAAO,eAAe,EAAE,YAAY,MAAM,CAAC,mBAAmB,OAAO,eAAe,CAAC,QAAQ;QAEjG,mBAAmB;QACnB,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI;QACjG,OAAO,oHAAA,CAAA,OAAI,CAAC,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OAAO,MAAmB;IAC7D,IAAI;QACF,uCAAuC;QACvC,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,KAAK,UAAU,CAAC,GAAG,CAAC,OAAO;YACzB,sBAAsB;YACtB,MAAM,eAAe,UAAU,MAAM,GAAG,MAAM,kBAAkB,UAAU,MAAM,IAAY;gBAAE,cAAc;gBAAI,SAAS;gBAAI,UAAU;YAAG;YAC1I,0BAA0B;YAC1B,MAAM,mBAAmB,UAAU,UAAU,GACzC,MAAM,kBAAkB,UAAU,UAAU,IAC5C;gBAAE,cAAc;gBAAI,SAAS;gBAAI,UAAU;YAAG;YAClD,OAAO;gBACL,MAAM,UAAU,IAAI;gBACpB,OAAO,UAAU,KAAK;gBACtB,QAAQ,UAAU,MAAM;gBACxB,oBAAoB,UAAU,cAAc,IAAI;gBAChD,aAAa,aAAa,YAAY;gBACtC,aAAa,aAAa,QAAQ;gBAClC,iBAAiB,iBAAiB,YAAY;gBAC9C,iBAAiB,iBAAiB,QAAQ;YAC5C;QACF;QAGF,yBAAyB;QACzB,MAAM,UAAU;YACd,QAAQ;YACR,YAAY;QACd;QAEA,sBAAsB;QACtB,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,uBAAuB,EAAE;IAClE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAQO,MAAM,0BAA0B,OAAO;IAC5C,OAAO,oHAAA,CAAA,OAAI,CAAC,IAAI,CAAC,4HAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,yBAAyB,EAAE;AACpE", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/jobRequirementConstant.ts"], "sourcesContent": ["import { JobSelectOption } from \"@/interfaces/jobRequirementesInterfaces\";\n\n/**\n * Job category options\n */\nexport const CATEGORY_OPTION: JobSelectOption[] = [\n  { label: \"Full time\", value: \"full_time\" },\n  { label: \"Part time\", value: \"part_time\" },\n  { label: \"Contract\", value: \"contract\" },\n  { label: \"Internship\", value: \"internship\" },\n  { label: \"Freelance\", value: \"freelance\" },\n];\n\n/**\n * Salary cycle options\n */\nexport const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Per Hour\", value: \"per hour\" },\n  { label: \"Per Month\", value: \"per month\" },\n  { label: \"Per Annum\", value: \"per annum\" },\n];\n\n/**\n * Location type options\n */\nexport const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [\n  { label: \"Remote\", value: \"remote\" },\n  { label: \"Hybrid\", value: \"hybrid\" },\n  { label: \"On-site\", value: \"onsite\" },\n];\n\n/**\n * Tone style options\n */\nexport const TONE_STYLE_OPTIONS: JobSelectOption[] = [\n  { label: \"Professional & Formal\", value: \"Professional_Formal\" },\n  { label: \"Conversational & Approachable\", value: \"Conversational_Approachable\" },\n  { label: \"Bold & Energetic\", value: \"Bold_Energetic\" },\n  { label: \"Inspirational & Mission-Driven\", value: \"Inspirational_Mission-Driven\" },\n  { label: \"Technical & Precise\", value: \"Technical_Precise\" },\n  { label: \"Creative & Fun\", value: \"Creative_Fun\" },\n  { label: \"Inclusive & Human-Centered\", value: \"Inclusive_Human-Centered\" },\n  { label: \"Minimalist & Straightforward\", value: \"Minimalist_Straightforward\" },\n];\n\n/**\n * Compliance options\n */\nexport const COMPLIANCE_OPTIONS: JobSelectOption[] = [\n  { label: \"Equal Employment Opportunity (EEO) Statement\", value: \"Equal Employment Opportunity (EEO) Statement\" },\n  {\n    label: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n    value: \"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\",\n  },\n  { label: \"Disability Accommodation Statement\", value: \"Disability Accommodation Statement\" },\n  {\n    label: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n    value: \"Veterans Preference Statement (For Government Agencies and Federal Contractors)\",\n  },\n  { label: \"Diversity & Inclusion Commitment\", value: \"Diversity & Inclusion Commitment\" },\n  {\n    label: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n    value: \"Pay Transparency Non-Discrimination Statement (For Federal Contractors)\",\n  },\n  {\n    label: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n    value: \"Background Check and Drug-Free Workplace Policy (If Applicable)\",\n  },\n  { label: \"Work Authorization & Immigration Statement\", value: \"Work Authorization & Immigration Statement\" },\n];\n\nexport const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [\n  { label: \"General\", value: \"General\" },\n  { label: \"No experience necessary\", value: \"No experience necessary\" },\n  { label: \"Entry-Level Position\", value: \"Entry-Level Position\" },\n  { label: \"Mid-Level Professional\", value: \"Mid-Level Professional\" },\n  { label: \"Senior/Experienced Professional\", value: \"Senior/Experienced Professional\" },\n  { label: \"Managerial/Executive Level\", value: \"Managerial/Executive Level\" },\n  { label: \"Specialized Expert\", value: \"Specialized Expert\" },\n];\n\nexport const DEPARTMENT_OPTION: JobSelectOption[] = [\n  { label: \"IT\", value: \"IT\" },\n  { label: \"HR\", value: \"HR\" },\n  { label: \"Marketing\", value: \"Marketing\" },\n  { label: \"Finance\", value: \"Finance\" },\n  { label: \"Sales\", value: \"Sales\" },\n];\n/**\n * Constants for file upload validation\n */\nexport const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const FILE_TYPE = \"application/pdf\";\nexport const FILE_NAME = \".pdf\";\n\n/**\n * Remove all $ and space symbols to clean the input\n */\nexport const SALARY_REMOVE_SYMBOL_REGEX = /[\\$\\s]/g;\n\n/**\n * Currency symbol\n */\nexport const CURRENCY_SYMBOL = \"$\";\n\n/**\n * Button list for SunEditor\n */\nexport const SUN_EDITOR_BUTTON_LIST = [\n  [\"font\", \"fontSize\", \"formatBlock\"],\n  [\"bold\", \"underline\", \"italic\"],\n  [\"fontColor\", \"hiliteColor\"],\n  [\"align\", \"list\", \"lineHeight\"],\n];\n\n/**\n * HiringType Select [Internal,External]\n */\nexport const HIRING_TYPE = {\n  INTERNAL: \"internal\",\n  EXTERNAL: \"external\",\n};\n\n/**\n * Skill categories\n */\nexport const SKILL_CATEGORY = {\n  Personal_Health: \"Personal Health\",\n  Social_Interaction: \"Social Interaction\",\n  Mastery_Of_Emotions: \"Mastery of Emotions\",\n  Mentality: \"Mentality\",\n  Cognitive_Abilities: \"Cognitive Abilities\",\n};\n\n/**\n * Application status values\n */\nexport const APPLICATION_STATUS = {\n  PENDING: \"Pending\",\n  APPROVED: \"Approved\",\n  REJECTED: \"Rejected\",\n  HIRED: \"Hired\",\n  ON_HOLD: \"On-Hold\",\n  FINAL_REJECT: \"Final-Reject\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport const SKILL_TYPE = {\n  ROLE: \"role\",\n  CULTURE: \"culture\",\n};\n\n/**\n * Skill type (for filtering/deselection logic etc.)\n */\nexport type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];\n\n/**\n * HiringType key for searchParams\n */\nexport const HIRING_TYPE_KEY = \"hiringType\";\n\nexport const CURSOR_POINT = { cursor: \"pointer\" };\n\nexport const COMPLIANCE_LINK = \"https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf\";\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAKO,MAAM,kBAAqC;IAChD;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,uBAA0C;IACrD;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAKM,MAAM,wBAA2C;IACtD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAS;CACrC;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAyB,OAAO;IAAsB;IAC/D;QAAE,OAAO;QAAiC,OAAO;IAA8B;IAC/E;QAAE,OAAO;QAAoB,OAAO;IAAiB;IACrD;QAAE,OAAO;QAAkC,OAAO;IAA+B;IACjF;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAkB,OAAO;IAAe;IACjD;QAAE,OAAO;QAA8B,OAAO;IAA2B;IACzE;QAAE,OAAO;QAAgC,OAAO;IAA6B;CAC9E;AAKM,MAAM,qBAAwC;IACnD;QAAE,OAAO;QAAgD,OAAO;IAA+C;IAC/G;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAsC,OAAO;IAAqC;IAC3F;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAoC,OAAO;IAAmC;IACvF;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA8C,OAAO;IAA6C;CAC5G;AAEM,MAAM,2BAA8C;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAA2B,OAAO;IAA0B;IACrE;QAAE,OAAO;QAAwB,OAAO;IAAuB;IAC/D;QAAE,OAAO;QAA0B,OAAO;IAAyB;IACnE;QAAE,OAAO;QAAmC,OAAO;IAAkC;IACrF;QAAE,OAAO;QAA8B,OAAO;IAA6B;IAC3E;QAAE,OAAO;QAAsB,OAAO;IAAqB;CAC5D;AAEM,MAAM,oBAAuC;IAClD;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAM,OAAO;IAAK;IAC3B;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAIM,MAAM,kBAAkB,IAAI,OAAO,MAAM,MAAM;AAC/C,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,YAAY;AAClB,MAAM,YAAY;AAKlB,MAAM,6BAA6B;AAKnC,MAAM,kBAAkB;AAKxB,MAAM,yBAAyB;IACpC;QAAC;QAAQ;QAAY;KAAc;IACnC;QAAC;QAAQ;QAAa;KAAS;IAC/B;QAAC;QAAa;KAAc;IAC5B;QAAC;QAAS;QAAQ;KAAa;CAChC;AAKM,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,iBAAiB;IAC5B,iBAAiB;IACjB,oBAAoB;IACpB,qBAAqB;IACrB,WAAW;IACX,qBAAqB;AACvB;AAKO,MAAM,qBAAqB;IAChC,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,cAAc;AAChB;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;AACX;AAUO,MAAM,kBAAkB;AAExB,MAAM,eAAe;IAAE,QAAQ;AAAU;AAEzC,MAAM,kBAAkB", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/CandidateApproveRejectModal.tsx"], "sourcesContent": ["\"use client\";\nimport React, { FC, useState } from \"react\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport InputWrapper from \"../formElements/InputWrapper\";\nimport Textarea from \"../formElements/Textarea\";\nimport { useForm } from \"react-hook-form\";\nimport { changeApplicationStatus } from \"@/services/screenResumeServices\";\nimport { useSelector } from \"react-redux\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\nimport { toastMessageSuccess, toTitleCase } from \"@/utils/helper\";\nimport { CandidateApplication, topCandidateApplication } from \"@/interfaces/candidatesInterface\";\n\ninterface IProps {\n  onClickCancel: () => void;\n  disabled?: boolean;\n  candidate?: CandidateApplication | topCandidateApplication;\n  onSuccess?: (candidate: CandidateApplication | topCandidateApplication, status: string) => void;\n}\n\nconst CandidateApproveRejectModal: FC<IProps> = ({ onClickCancel, candidate, onSuccess }) => {\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [success, setSuccess] = useState(false);\n  const [selectedStatus, setSelectedStatus] = useState<(typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS]>(APPLICATION_STATUS.ON_HOLD);\n\n  const {\n    control,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<{ reason: string }>({\n    defaultValues: {\n      reason: \"\",\n    },\n    mode: \"onSubmit\",\n    criteriaMode: \"firstError\",\n    shouldFocusError: true,\n    reValidateMode: \"onChange\",\n    resolver: (values) => {\n      const errors: Record<string, { type: string; message: string }> = {};\n\n      // Required validation for reason field\n      if (!values.reason || values.reason.trim() === \"\") {\n        errors.reason = {\n          type: \"required\",\n          message: \"Please provide a reason\",\n        };\n      } else if (values.reason.trim().length < 5) {\n        errors.reason = {\n          type: \"minLength\",\n          message: \"Reason should be at least 5 characters long\",\n        };\n      } else if (values.reason.trim().length > 50) {\n        errors.reason = {\n          type: \"maxLength\",\n          message: \"Reason should not exceed 50 characters\",\n        };\n      }\n\n      return {\n        values,\n        errors,\n      };\n    },\n  });\n\n  const onSubmit = async (formData: { reason: string }) => {\n    if (!candidate || !authData) return;\n\n    try {\n      setIsSubmitting(true);\n      setError(\"\");\n\n      const data = {\n        job_id: candidate.job_id,\n        candidate_id: candidate.candidateId,\n        hiring_manager_id: authData.id,\n        status: selectedStatus,\n        hiring_manager_reason: formData.reason,\n      };\n\n      const response = await changeApplicationStatus(data);\n\n      if (response.data && response.data.success) {\n        toastMessageSuccess(\"Candidate status has been updated successfully!\");\n        setSuccess(true);\n        // Call the onSuccess callback if provided\n        if (onSuccess) {\n          setTimeout(() => {\n            onClickCancel();\n            onSuccess(candidate, selectedStatus);\n          }, 1500);\n        }\n      } else {\n        setError(response.data?.message || \"Failed to update candidate status\");\n      }\n    } catch (err) {\n      console.error(\"Error updating candidate status:\", err);\n      setError(\"An unexpected error occurred. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-center\">\n            <h2>Review Candidate</h2>\n            <p>Please review the candidate's profile and make a decision.</p>\n            {!isSubmitting && (\n              <Button className=\"modal-close-btn\" onClick={onClickCancel}>\n                <ModalCloseIcon />\n              </Button>\n            )}\n          </div>\n          <div className=\"modal-body\">\n            {/* qualification-card */}\n            <div className=\"qualification-card\">\n              <div className=\"qualification-card-top\">\n                <div className=\"name\">\n                  <h3>{toTitleCase(candidate?.candidateName || \"Candidate\")}</h3>\n                  <p>{candidate?.aiDecision || \"Pending\"} by S9 Interviews</p>\n                </div>\n                <div className=\"top-right\">\n                  <div className=\"on-hold-status\">\n                    <p>{candidate?.applicationStatus}</p>\n                  </div>\n                </div>\n              </div>\n              <div className=\"qualification-card-mid\">\n                <p>\n                  <b>Reasons why they are a good match:</b>\n                </p>\n                <p>{candidate?.aiReason || \"No reason provided by AI evaluation.\"}</p>\n              </div>\n            </div>\n\n            {!success && (\n              <>\n                {/* Decision selection moved to the submit buttons */}\n\n                <form onSubmit={handleSubmit(onSubmit)}>\n                  <InputWrapper>\n                    <InputWrapper.Label htmlFor=\"reason\" required>\n                      Reason\n                    </InputWrapper.Label>\n                    <Textarea rows={6} name=\"reason\" control={control} placeholder=\"Enter your reason here\" className=\"form-control\" />\n                    {errors.reason && <p className=\"text-danger mt-1\">{errors.reason.message as string}</p>}\n                  </InputWrapper>\n\n                  {error && <div className=\"error-message alert alert-danger my-3\">{error}</div>}\n\n                  <div className=\"action-btn gap-3 mt-4\">\n                    {isSubmitting ? (\n                      <div className=\"text-center w-100\">\n                        <div className=\"spinner-border text-primary\" role=\"status\">\n                          <span className=\"visually-hidden\">Submitting...</span>\n                        </div>\n                        <p className=\"mt-2\">Submitting...</p>\n                      </div>\n                    ) : (\n                      <>\n                        <Button\n                          type=\"button\"\n                          className=\"primary-btn rounded-md w-100\"\n                          onClick={() => {\n                            setSelectedStatus(APPLICATION_STATUS.APPROVED);\n                            handleSubmit(onSubmit)();\n                          }}\n                        >\n                          Approve Candidate\n                        </Button>\n                        <Button\n                          type=\"button\"\n                          className=\"danger-btn rounded-md w-100\"\n                          onClick={() => {\n                            setSelectedStatus(APPLICATION_STATUS.REJECTED);\n                            handleSubmit(onSubmit)();\n                          }}\n                        >\n                          Reject Candidate\n                        </Button>\n                      </>\n                    )}\n                  </div>\n                </form>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CandidateApproveRejectModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAXA;;;;;;;;;;;;AAqBA,MAAM,8BAA0C,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE;IACtF,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgE,0IAAA,CAAA,qBAAkB,CAAC,OAAO;IAE7I,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,eAAe;YACb,QAAQ;QACV;QACA,MAAM;QACN,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,UAAU,CAAC;YACT,MAAM,SAA4D,CAAC;YAEnE,uCAAuC;YACvC,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,IAAI;gBACjD,OAAO,MAAM,GAAG;oBACd,MAAM;oBACN,SAAS;gBACX;YACF,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC1C,OAAO,MAAM,GAAG;oBACd,MAAM;oBACN,SAAS;gBACX;YACF,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;gBAC3C,OAAO,MAAM,GAAG;oBACd,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,OAAO;gBACL;gBACA;YACF;QACF;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,aAAa,CAAC,UAAU;QAE7B,IAAI;YACF,gBAAgB;YAChB,SAAS;YAET,MAAM,OAAO;gBACX,QAAQ,UAAU,MAAM;gBACxB,cAAc,UAAU,WAAW;gBACnC,mBAAmB,SAAS,EAAE;gBAC9B,QAAQ;gBACR,uBAAuB,SAAS,MAAM;YACxC;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,0BAAuB,AAAD,EAAE;YAE/C,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBAC1C,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;gBACpB,WAAW;gBACX,0CAA0C;gBAC1C,IAAI,WAAW;oBACb,WAAW;wBACT;wBACA,UAAU,WAAW;oBACvB,GAAG;gBACL;YACF,OAAO;gBACL,SAAS,SAAS,IAAI,EAAE,WAAW;YACrC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;4BACF,CAAC,8BACA,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;0CAC3C,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAI,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,WAAW,iBAAiB;;;;;;kEAC7C,8OAAC;;4DAAG,WAAW,cAAc;4DAAU;;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DACC,cAAA,8OAAC;8DAAE;;;;;;;;;;;0DAEL,8OAAC;0DAAG,WAAW,YAAY;;;;;;;;;;;;;;;;;;4BAI9B,CAAC,yBACA;0CAGE,cAAA,8OAAC;oCAAK,UAAU,aAAa;;sDAC3B,8OAAC,kJAAA,CAAA,UAAY;;8DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;oDAAC,SAAQ;oDAAS,QAAQ;8DAAC;;;;;;8DAG9C,8OAAC,8IAAA,CAAA,UAAQ;oDAAC,MAAM;oDAAG,MAAK;oDAAS,SAAS;oDAAS,aAAY;oDAAyB,WAAU;;;;;;gDACjG,OAAO,MAAM,kBAAI,8OAAC;oDAAE,WAAU;8DAAoB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;wCAGzE,uBAAS,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;sDAElE,8OAAC;4CAAI,WAAU;sDACZ,6BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAA8B,MAAK;kEAChD,cAAA,8OAAC;4DAAK,WAAU;sEAAkB;;;;;;;;;;;kEAEpC,8OAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;qEAGtB;;kEACE,8OAAC,4IAAA,CAAA,UAAM;wDACL,MAAK;wDACL,WAAU;wDACV,SAAS;4DACP,kBAAkB,0IAAA,CAAA,qBAAkB,CAAC,QAAQ;4DAC7C,aAAa;wDACf;kEACD;;;;;;kEAGD,8OAAC,4IAAA,CAAA,UAAM;wDACL,MAAK;wDACL,WAAU;wDACV,SAAS;4DACP,kBAAkB,0IAAA,CAAA,qBAAkB,CAAC,QAAQ;4DAC7C,aAAa;wDACf;kEACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAczB;uCAEe", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/svgComponents/BackArrowIcon.tsx"], "sourcesContent": ["import React from \"react\";\n\ntype BackArrowIconProps = {\n  onClick?: React.MouseEventHandler<SVGSVGElement>;\n};\n\nfunction BackArrowIcon({ onClick }: BackArrowIconProps) {\n  return (\n    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"cursor-pointer me-3\" width=\"26\" height=\"26\" viewBox=\"0 0 32 32\" fill=\"none\" onClick={onClick}>\n      <path\n        d=\"M28.6843 14.6661H5.23629L12.2936 7.60879C12.421 7.48579 12.5225 7.33867 12.5924 7.176C12.6623 7.01332 12.6991 6.83836 12.7006 6.66133C12.7022 6.48429 12.6684 6.30871 12.6014 6.14485C12.5343 5.98099 12.4353 5.83212 12.3101 5.70693C12.185 5.58174 12.0361 5.48274 11.8722 5.41569C11.7084 5.34865 11.5328 5.31492 11.3558 5.31646C11.1787 5.318 11.0038 5.35478 10.8411 5.42466C10.6784 5.49453 10.5313 5.59611 10.4083 5.72346L1.07495 15.0568C0.824991 15.3068 0.68457 15.6459 0.68457 15.9995C0.68457 16.353 0.824991 16.6921 1.07495 16.9421L10.4083 26.2755C10.6598 26.5183 10.9966 26.6527 11.3462 26.6497C11.6957 26.6467 12.0302 26.5064 12.2774 26.2592C12.5246 26.012 12.6648 25.6776 12.6679 25.328C12.6709 24.9784 12.5365 24.6416 12.2936 24.3901L5.23629 17.3328H28.6843C29.0379 17.3328 29.377 17.1923 29.6271 16.9423C29.8771 16.6922 30.0176 16.3531 30.0176 15.9995C30.0176 15.6458 29.8771 15.3067 29.6271 15.0566C29.377 14.8066 29.0379 14.6661 28.6843 14.6661Z\"\n        fill=\"#333333\"\n      />\n    </svg>\n  );\n}\n\nexport default BackArrowIcon;\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,cAAc,EAAE,OAAO,EAAsB;IACpD,qBACE,8OAAC;QAAI,OAAM;QAA6B,WAAU;QAAsB,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,SAAS;kBACtI,cAAA,8OAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;AAIb;uCAEe", "debugId": null}}, {"offset": {"line": 1019, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1025, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonModals/ArchiveCandidateModal.tsx"], "sourcesContent": ["\"use client\";\nimport { FC, useState } from \"react\";\nimport Button from \"../formElements/Button\";\nimport ModalCloseIcon from \"../svgComponents/ModalCloseIcon\";\nimport InputWrapper from \"../formElements/InputWrapper\";\nimport Textarea from \"../formElements/Textarea\";\nimport { useForm } from \"react-hook-form\";\nimport { useSelector } from \"react-redux\";\nimport { AuthState } from \"@/redux/slices/authSlice\";\n\ninterface ArchiveCandidateModalProps {\n  onClickCancel: () => void;\n  applicationId: number;\n  jobId: number;\n  onSuccess?: (reason: string) => void;\n}\n\nconst ArchiveCandidateModal: FC<ArchiveCandidateModalProps> = ({ onClickCancel, onSuccess }) => {\n  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState(\"\");\n  const {\n    control,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<{ reason: string }>({\n    defaultValues: { reason: \"\" },\n    mode: \"onSubmit\",\n    criteriaMode: \"firstError\",\n    shouldFocusError: true,\n    reValidateMode: \"onChange\",\n    resolver: (values) => {\n      const errors: Record<string, { type: string; message: string }> = {};\n      if (!values.reason || values.reason.trim() === \"\") {\n        errors.reason = { type: \"required\", message: \"Please provide a reason\" };\n      }\n      return { values, errors };\n    },\n  });\n\n  const onSubmit = async (data: { reason: string }) => {\n    if (!authData) return;\n    try {\n      setIsSubmitting(true);\n      setError(\"\");\n\n      if (onSuccess) await onSuccess(data.reason);\n    } catch (err) {\n      console.error(\"Archive candidate error:\", err);\n      setError(\"An unexpected error occurred. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"modal theme-modal show-modal\">\n      <div className=\"modal-dialog modal-dialog-centered\">\n        <div className=\"modal-content\">\n          <div className=\"modal-header justify-content-between\">\n            <h2>Archive Candidate</h2>\n            <Button className=\"modal-close-btn\" onClick={onClickCancel} disabled={isSubmitting}>\n              <ModalCloseIcon />\n            </Button>\n          </div>\n          <div className=\"modal-body\">\n            <p>Please provide a reason for archiving this candidate:</p>\n            <form onSubmit={handleSubmit(onSubmit)}>\n              <InputWrapper>\n                <InputWrapper.Label htmlFor=\"reason\" required>\n                  Reason\n                </InputWrapper.Label>\n                <Textarea rows={5} name=\"reason\" control={control} placeholder=\"Enter reason for archiving candidate\" className=\"form-control\" />\n                {errors.reason && <p className=\"text-danger mt-1\">{errors.reason.message}</p>}\n              </InputWrapper>\n\n              {error && <div className=\"alert alert-danger my-3\">{error}</div>}\n\n              <div className=\"d-flex justify-content-end gap-2 mt-4\">\n                <Button type=\"button\" className=\"secondary-btn rounded-md\" onClick={onClickCancel} disabled={isSubmitting}>\n                  Cancel\n                </Button>\n                <Button type=\"submit\" className=\"primary-btn rounded-md\" disabled={isSubmitting}>\n                  {isSubmitting ? \"Archiving...\" : \"Archive\"}\n                </Button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ArchiveCandidateModal;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AAiBA,MAAM,wBAAwD,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE;IACzF,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,eAAe;YAAE,QAAQ;QAAG;QAC5B,MAAM;QACN,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,UAAU,CAAC;YACT,MAAM,SAA4D,CAAC;YACnE,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,IAAI;gBACjD,OAAO,MAAM,GAAG;oBAAE,MAAM;oBAAY,SAAS;gBAA0B;YACzE;YACA,OAAO;gBAAE;gBAAQ;YAAO;QAC1B;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,UAAU;QACf,IAAI;YACF,gBAAgB;YAChB,SAAS;YAET,IAAI,WAAW,MAAM,UAAU,KAAK,MAAM;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAG;;;;;;0CACJ,8OAAC,4IAAA,CAAA,UAAM;gCAAC,WAAU;gCAAkB,SAAS;gCAAe,UAAU;0CACpE,cAAA,8OAAC,qJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAK,UAAU,aAAa;;kDAC3B,8OAAC,kJAAA,CAAA,UAAY;;0DACX,8OAAC,kJAAA,CAAA,UAAY,CAAC,KAAK;gDAAC,SAAQ;gDAAS,QAAQ;0DAAC;;;;;;0DAG9C,8OAAC,8IAAA,CAAA,UAAQ;gDAAC,MAAM;gDAAG,MAAK;gDAAS,SAAS;gDAAS,aAAY;gDAAuC,WAAU;;;;;;4CAC/G,OAAO,MAAM,kBAAI,8OAAC;gDAAE,WAAU;0DAAoB,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;oCAGzE,uBAAS,8OAAC;wCAAI,WAAU;kDAA2B;;;;;;kDAEpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4IAAA,CAAA,UAAM;gDAAC,MAAK;gDAAS,WAAU;gDAA2B,SAAS;gDAAe,UAAU;0DAAc;;;;;;0DAG3G,8OAAC,4IAAA,CAAA,UAAM;gDAAC,MAAK;gDAAS,WAAU;gDAAyB,UAAU;0DAChE,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;uCAEe", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/CandidatesServices/candidatesApplicationServices.ts"], "sourcesContent": ["import endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport {\n  AdditionalInfoPayload,\n  CandidateApplication,\n  CandidateProfileResponse,\n  PromoteDemotePayload,\n  topCandidateApplication,\n} from \"@/interfaces/candidatesInterface\";\n\n/**\n * Fetch candidates with their job applications.\n *\n * Automatically sends query parameters to backend, including:\n * - jobId: Filter by job\n * - searchStr: For candidate name search\n * - isActive: true for active, false for archived\n * - page: For pagination (used as offset)\n * - limit: Number of items per page\n */\nexport const fetchCandidatesApplications = (data: {\n  page?: number; // Offset for pagination\n  limit?: number; // Max results per page\n  searchStr?: string; // Search candidates by name\n  isActive?: boolean; // Filter: true = active, false = archived\n  jobId?: number; // Optional jobId filter\n}): Promise<ApiResponse<CandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, { ...data });\n};\n\nexport const fetchTopCandidatesApplications = (jobId?: number): Promise<ApiResponse<topCandidateApplication[]>> => {\n  return http.get(endpoint.candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {\n    jobId, // Optional jobId filter\n  });\n};\n\nexport const promoteDemoteCandidate = async (payload: PromoteDemotePayload): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);\n};\n\nexport const addApplicantAdditionalInfo = async (payload: AdditionalInfoPayload): Promise<ApiResponse<null>> => {\n  return await http.post(endpoint.candidatesApplication.ADDITIONAL_INFO, payload);\n};\n\n/**\n * Fetches candidate profile details by candidate ID\n * @param candidateId - The ID of the candidate\n */\nexport const fetchCandidateProfile = (candidateId: number | string): Promise<ApiResponse<CandidateProfileResponse>> => {\n  return http.get(endpoint.candidatesApplication.GET_CANDIDATE_DETAILS, { candidateId });\n};\n\n/**\n * Updates the job application status (Hire/Reject)\n * @param jobApplicationId - The ID of the job application to update\n * @param payload - The status payload (\"Final-Hired\" or \"Rejected\")\n */\nexport const updateJobApplicationStatus = async (jobApplicationId: number, status: string): Promise<ApiResponse<null>> => {\n  return await http.put(endpoint.candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(\":jobApplicationId\", jobApplicationId.toString()), {\n    status,\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAoBO,MAAM,8BAA8B,CAAC;IAO1C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,gCAAgC,EAAE;QAAE,GAAG,IAAI;IAAC;AAC7F;AAEO,MAAM,iCAAiC,CAAC;IAC7C,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,oCAAoC,EAAE;QACnF;IACF;AACF;AAEO,MAAM,yBAAyB,OAAO;IAC3C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,wBAAwB,EAAE;AACjF;AAEO,MAAM,6BAA6B,OAAO;IAC/C,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,OAAS,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,eAAe,EAAE;AACzE;AAMO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,qBAAqB,EAAE;QAAE;IAAY;AACtF;AAOO,MAAM,6BAA6B,OAAO,kBAA0B;IACzE,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,qBAAqB,iBAAiB,QAAQ,KAAK;QAC5I;IACF;AACF", "debugId": null}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/services/CandidatesServices/candidatesApplicationStatusUpdateService.ts"], "sourcesContent": ["// candidatesApplicationServices.ts\n\nimport endpoint from \"@/constants/endpoint\";\nimport * as http from \"@/utils/http\";\nimport { ApiResponse } from \"@/interfaces/commonInterfaces\";\nimport { UpdateCandidateApplicationStatusResponse } from \"@/interfaces/jobApplicationInterface\";\n\n/**\n * Archive or unarchive a candidate application\n */\nexport const archiveActiveApplication = (\n  applicationId: number,\n  isActive: boolean,\n  reason?: string\n): Promise<ApiResponse<UpdateCandidateApplicationStatusResponse>> => {\n  return http.put(endpoint.candidatesApplication.ARCHIVE_ACTIVE_APPLICATION.replace(\":applicationId\", applicationId.toString()), {\n    isActive,\n    reason,\n  });\n};\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;AAEnC;AACA;;;AAOO,MAAM,2BAA2B,CACtC,eACA,UACA;IAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,MAAQ,AAAD,EAAE,4HAAA,CAAA,UAAQ,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,OAAO,CAAC,kBAAkB,cAAc,QAAQ,KAAK;QAC7H;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/commonPage.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"commonPage-module-scss-module__em0r7a__active\",\n  \"add_another_candidate_link\": \"commonPage-module-scss-module__em0r7a__add_another_candidate_link\",\n  \"approved_status_indicator\": \"commonPage-module-scss-module__em0r7a__approved_status_indicator\",\n  \"border_none\": \"commonPage-module-scss-module__em0r7a__border_none\",\n  \"candidate_card\": \"commonPage-module-scss-module__em0r7a__candidate_card\",\n  \"candidate_card_header\": \"commonPage-module-scss-module__em0r7a__candidate_card_header\",\n  \"candidate_qualification_page\": \"commonPage-module-scss-module__em0r7a__candidate_qualification_page\",\n  \"candidates_list_page\": \"commonPage-module-scss-module__em0r7a__candidates_list_page\",\n  \"candidates_list_section\": \"commonPage-module-scss-module__em0r7a__candidates_list_section\",\n  \"career-skill-card\": \"commonPage-module-scss-module__em0r7a__career-skill-card\",\n  \"dashboard__stat\": \"commonPage-module-scss-module__em0r7a__dashboard__stat\",\n  \"dashboard__stat_design\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_design\",\n  \"dashboard__stat_image\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_image\",\n  \"dashboard__stat_label\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_label\",\n  \"dashboard__stat_value\": \"commonPage-module-scss-module__em0r7a__dashboard__stat_value\",\n  \"dashboard__stats\": \"commonPage-module-scss-module__em0r7a__dashboard__stats\",\n  \"dashboard_inner_head\": \"commonPage-module-scss-module__em0r7a__dashboard_inner_head\",\n  \"dashboard_page\": \"commonPage-module-scss-module__em0r7a__dashboard_page\",\n  \"header_tab\": \"commonPage-module-scss-module__em0r7a__header_tab\",\n  \"inner_heading\": \"commonPage-module-scss-module__em0r7a__inner_heading\",\n  \"inner_page\": \"commonPage-module-scss-module__em0r7a__inner_page\",\n  \"input_type_file\": \"commonPage-module-scss-module__em0r7a__input_type_file\",\n  \"interview_form_icon\": \"commonPage-module-scss-module__em0r7a__interview_form_icon\",\n  \"job_info\": \"commonPage-module-scss-module__em0r7a__job_info\",\n  \"job_page\": \"commonPage-module-scss-module__em0r7a__job_page\",\n  \"manual_upload_resume\": \"commonPage-module-scss-module__em0r7a__manual_upload_resume\",\n  \"operation_admins_img\": \"commonPage-module-scss-module__em0r7a__operation_admins_img\",\n  \"resume_page\": \"commonPage-module-scss-module__em0r7a__resume_page\",\n  \"search_box\": \"commonPage-module-scss-module__em0r7a__search_box\",\n  \"section_heading\": \"commonPage-module-scss-module__em0r7a__section_heading\",\n  \"section_name\": \"commonPage-module-scss-module__em0r7a__section_name\",\n  \"selected\": \"commonPage-module-scss-module__em0r7a__selected\",\n  \"selecting\": \"commonPage-module-scss-module__em0r7a__selecting\",\n  \"selection\": \"commonPage-module-scss-module__em0r7a__selection\",\n  \"skills_info_box\": \"commonPage-module-scss-module__em0r7a__skills_info_box\",\n  \"skills_tab\": \"commonPage-module-scss-module__em0r7a__skills_tab\",\n  \"text_xs\": \"commonPage-module-scss-module__em0r7a__text_xs\",\n  \"upload_resume_page\": \"commonPage-module-scss-module__em0r7a__upload_resume_page\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/constants/screenResumeConstant.ts"], "sourcesContent": ["export const GENDER_OPTIONS = [\n  { value: \"Male\", label: \"Male\" },\n  { value: \"Female\", label: \"Female\" },\n];\n\nexport enum InterviewTabType {\n  UPCOMING = \"UpcomingInterviews\",\n  PAST = \"PastInterviews\",\n}\n\nexport const APPLICATION_UPDATE_STATUS = {\n  PROMOTED: \"Promoted\",\n  DEMOTED: \"Demoted\",\n};\n\nexport type APPLICATION_UPDATE_STATUS = (typeof APPLICATION_UPDATE_STATUS)[keyof typeof APPLICATION_UPDATE_STATUS];\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAEM,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,MAAM,4BAA4B;IACvC,UAAU;IACV,SAAS;AACX", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/skeletons/TableSkeleton.tsx"], "sourcesContent": ["import React from \"react\";\nimport Skeleton from \"react-loading-skeleton\";\nimport \"react-loading-skeleton/dist/skeleton.css\";\n\nconst TableSkeleton = ({ rows = 3, cols = 3, colWidths = \"120,80,100\" }) => {\n  const columnWidths = colWidths.split(\",\").map((w) => w.trim());\n\n  return (\n    <tbody>\n      {[...Array(rows)].map((_, rowIndex) => (\n        <tr key={`loader-row-${rowIndex}`}>\n          {[...Array(cols)].map((_, colIndex) => (\n            <td key={`loader-col-${colIndex}`} className=\"text-center\">\n              <Skeleton width={columnWidths[colIndex] || 80} height={20} circle={false} />\n            </td>\n          ))}\n        </tr>\n      ))}\n    </tbody>\n  );\n};\n\nexport default TableSkeleton;\n"], "names": [], "mappings": ";;;;AACA;;;;AAGA,MAAM,gBAAgB,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,YAAY,YAAY,EAAE;IACrE,MAAM,eAAe,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;IAE3D,qBACE,8OAAC;kBACE;eAAI,MAAM;SAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,8OAAC;0BACE;uBAAI,MAAM;iBAAM,CAAC,GAAG,CAAC,CAAC,GAAG,yBACxB,8OAAC;wBAAkC,WAAU;kCAC3C,cAAA,8OAAC,6JAAA,CAAA,UAAQ;4BAAC,OAAO,YAAY,CAAC,SAAS,IAAI;4BAAI,QAAQ;4BAAI,QAAQ;;;;;;uBAD5D,CAAC,WAAW,EAAE,UAAU;;;;;eAF5B,CAAC,WAAW,EAAE,UAAU;;;;;;;;;;AAUzC;uCAEe", "debugId": null}}, {"offset": {"line": 1427, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1438, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/public/assets/fullPageLoader.gif.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1200, height: 857, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAK,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1457, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/commonComponent/FullPageLoader.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport fullPageLoaderGif from \"../../../public/assets/fullPageLoader.gif\";\n\nconst FullPageLoader = () => {\n  return (\n    <div className=\"flex items-center justify-center h-screen\">\n      <Image src={fullPageLoaderGif} alt=\"Loading...\" />\n    </div>\n  );\n};\n\nexport default FullPageLoader;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,iBAAiB;IACrB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK,oSAAA,CAAA,UAAiB;YAAE,KAAI;;;;;;;;;;;AAGzC;uCAEe", "debugId": null}}, {"offset": {"line": 1484, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/components/views/resume/CandidatesList.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\n\"use client\";\n\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport dayjs from \"dayjs\";\n// import { useForm } from \"react-hook-form\";\nimport { useRouter } from \"next/navigation\";\nimport { useSelector } from \"react-redux\";\n// import InfiniteScroll from \"react-infinite-scroll-component\";\n\nimport Button from \"@/components/formElements/Button\";\n// import InputWrapper from \"@/components/formElements/InputWrapper\";\n// import Textbox from \"@/components/formElements/Textbox\";\n// import SearchIcon from \"@/components/svgComponents/SearchIcon\";\nimport ThreeDotsIcon from \"@/components/svgComponents/ThreeDotsIcon\";\nimport CandidateApproveRejectModal from \"@/components/commonModals/CandidateApproveRejectModal\";\nimport BackArrowIcon from \"@/components/svgComponents/BackArrowIcon\";\nimport ArchiveCandidateModal from \"@/components/commonModals/ArchiveCandidateModal\"; //\n\n// Services\nimport {\n  fetchCandidatesApplications,\n  fetchTopCandidatesApplications,\n  promoteDemoteCandidate,\n} from \"@/services/CandidatesServices/candidatesApplicationServices\";\nimport { archiveActiveApplication } from \"@/services/CandidatesServices/candidatesApplicationStatusUpdateService\";\n\nimport { AuthState } from \"@/redux/slices/authSlice\";\nimport { DEFAULT_LIMIT, PERMISSION } from \"@/constants/commonConstants\";\nimport { APPLICATION_STATUS } from \"@/constants/jobRequirementConstant\";\n\nimport style from \"@/styles/commonPage.module.scss\";\nimport { useTranslations } from \"use-intl\";\nimport { CandidateApplication, PromoteDemotePayload, topCandidateApplication } from \"@/interfaces/candidatesInterface\";\nimport \"react-loading-skeleton/dist/skeleton.css\";\nimport InfiniteScroll from \"react-infinite-scroll-component\";\nimport { APPLICATION_UPDATE_STATUS } from \"@/constants/screenResumeConstant\";\nimport { toastMessageError, toTitleCase } from \"@/utils/helper\";\nimport TableSkeleton from \"../skeletons/TableSkeleton\";\nimport ROUTES from \"@/constants/routes\";\nimport { debounce } from \"lodash\";\nimport FullPageLoader from \"@/components/commonComponent/FullPageLoader\";\n// import InputWrapper from \"@/components/formElements/InputWrapper\";\n// import Textbox from \"@/components/formElements/Textbox\";\n// import SearchIcon from \"@/components/svgComponents/SearchIcon\";\n// import { useForm } from \"react-hook-form\";\n\nconst CandidatesList = ({\n  params,\n  searchParams,\n}: {\n  params: Promise<{ jobId: string }>;\n  searchParams: Promise<{ title: string; jobUniqueId: string }>;\n}) => {\n  // const { control } = useForm();\n  const userData = useSelector((state: { auth: AuthState }) => state.auth.authData);\n  const userPermissions = useSelector((state: { auth: AuthState }) => state.auth.permissions || []) as unknown as string[];\n  const hasArchiveRestoreCandidatesPermission = userPermissions.includes(PERMISSION.ARCHIVE_RESTORE_CANDIDATES);\n  const router = useRouter();\n\n  const paramsPromise = React.use(params);\n  const searchParamsPromise = React.use(searchParams);\n\n  // State for candidates data\n  const [candidates, setCandidates] = useState<CandidateApplication[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [loader, setLoader] = useState(false);\n  const [hasMore, setHasMore] = useState(true);\n  const [offset, setOffset] = useState(0);\n  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);\n  const [selectedCandidate, setSelectedCandidate] = useState<CandidateApplication | topCandidateApplication | null>(null);\n  const [showReviewModal, setShowReviewModal] = useState(false);\n  const [showArchiveModal, setShowArchiveModal] = useState(false); //\n  const [searchStr] = useState(\"\");\n  const [topCandidates, setTopCandidates] = useState<topCandidateApplication[]>([]);\n  const [disable, setDisable] = useState(false);\n  const t = useTranslations();\n  const dropdownRefs = React.useRef<{ [key: string]: HTMLUListElement | null }>({});\n  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);\n\n  const [loadingFullScreen, setFullScreenLoading] = useState(false);\n\n  useEffect(() => {\n    if (!Number(paramsPromise.jobId) || !searchParamsPromise.title) {\n      router.push(ROUTES.DASHBOARD);\n    }\n  }, [paramsPromise.jobId]);\n\n  // const observer = useRef<IntersectionObserver | null>(null);\n\n  const fetchTopCandidates = async () => {\n    if (!userData?.orgId || !paramsPromise?.jobId) return;\n\n    setLoading(true);\n    try {\n      const response = await fetchTopCandidatesApplications(Number(paramsPromise.jobId));\n      if (response?.data?.success) {\n        setTopCandidates(response.data.data);\n      } else {\n        setTopCandidates([]);\n      }\n    } catch {\n      setTopCandidates([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePromoteDemoteCandidate = async (candidate: CandidateApplication | topCandidateApplication, action: APPLICATION_UPDATE_STATUS) => {\n    setDisable(true);\n    setFullScreenLoading(true);\n    try {\n      const payload = {\n        candidateId: candidate.candidateId,\n        applicationId: candidate.applicationId,\n        action,\n      };\n\n      const response = await promoteDemoteCandidate(payload as PromoteDemotePayload);\n\n      if (response?.data?.success) {\n        if (action === APPLICATION_UPDATE_STATUS.PROMOTED) {\n          // Candidate is currently in the 'other candidates' list\n          const fromOther = candidate as CandidateApplication;\n\n          // Remove from other candidates\n          setCandidates((prev) => prev.filter((c) => c.applicationId !== fromOther.applicationId));\n\n          // Add to top candidates\n          const promoted: topCandidateApplication = {\n            candidateName: fromOther.candidateName,\n            applicationCreatedTs: fromOther.applicationCreatedTs,\n            atsScore: fromOther.atsScore,\n            applicationId: fromOther.applicationId,\n            applicationRankStatus: APPLICATION_UPDATE_STATUS.PROMOTED,\n            candidateId: fromOther.candidateId,\n            aiReason: fromOther.aiReason,\n            aiDecision: fromOther.aiDecision,\n            applicationStatus: fromOther.applicationStatus,\n            hiringManagerReason: fromOther.hiringManagerReason,\n            applicationUpdatedTs: new Date().toISOString(),\n            applicationSource: fromOther.applicationSource || \"\", // Ensure this is set\n            job_id: fromOther.job_id || 0, // replace if you have this info\n            isTopApplication: fromOther.isTopApplication || false,\n          };\n\n          setTopCandidates((prev) => [...prev, promoted]);\n        } else if (action === APPLICATION_UPDATE_STATUS.DEMOTED) {\n          // Candidate is currently in the 'top candidates' list\n          const fromTop = candidate as topCandidateApplication;\n\n          // Remove from top candidates\n          setTopCandidates((prev) => prev.filter((c) => c.applicationId !== fromTop.applicationId));\n\n          // Add to other candidates\n          const demoted: CandidateApplication = {\n            candidateId: fromTop.candidateId,\n            candidateName: fromTop.candidateName,\n            applicationId: fromTop.applicationId,\n            applicationStatus: \"\",\n            applicationSource: \"\",\n            applicationCreatedTs: fromTop.applicationCreatedTs,\n            applicationUpdatedTs: new Date().toISOString(),\n            isActive: true,\n            job_id: 0, // replace if you have this info\n            hiring_manager_id: 0,\n            hiringManagerReason: \"\",\n            applicationRankStatus: APPLICATION_UPDATE_STATUS.DEMOTED,\n            atsScore: fromTop.atsScore,\n            aiReason: fromTop.aiReason,\n            aiDecision: fromTop.aiDecision,\n          };\n\n          setCandidates((prev) => [...prev, demoted]);\n        }\n      } else {\n        toastMessageError(t(response?.data?.message));\n      }\n    } catch {\n    } finally {\n      setActiveDropdown(null);\n      setDisable(false);\n      setFullScreenLoading(false);\n    }\n  };\n\n  const fetchMoreCandidatesApplications = useCallback(async (currentOffset = 0, reset = false, searchStr: string = \"\") => {\n    if (!userData?.orgId) return;\n    setLoader(true);\n    try {\n      const response = await fetchCandidatesApplications({\n        page: currentOffset,\n        limit: DEFAULT_LIMIT,\n        searchStr: searchStr,\n        isActive: true,\n        jobId: Number(paramsPromise.jobId),\n      });\n\n      console.log(\"response\", response);\n      if (response?.data?.success) {\n        const newCandidates: CandidateApplication[] = response.data.data.data;\n\n        console.log(\"newCandidates\", newCandidates);\n\n        setCandidates((prev) => (reset ? newCandidates : [...prev, ...newCandidates]));\n        if (newCandidates.length < DEFAULT_LIMIT) {\n          setHasMore(false);\n        } else {\n          setHasMore(true);\n        }\n        setOffset(currentOffset + newCandidates.length);\n      } else {\n        setHasMore(false);\n      }\n    } catch {\n      setHasMore(false);\n    } finally {\n      setLoader(false);\n    }\n  }, []);\n\n  const loadMoreCandidates = () => {\n    console.log(\"loadMoreCandidates called=================>\");\n    if (!loader && hasMore) fetchMoreCandidatesApplications(offset, false, searchStr);\n  };\n\n  // const lastElementRef = useCallback(\n  //   (node: HTMLElement | null) => {\n  //     if (loading) return;\n  //     if (observer.current) observer.current.disconnect();\n  //     observer.current = new IntersectionObserver((entries) => {\n  //       if (entries[0].isIntersecting && hasMore) loadMoreCandidates();\n  //     });\n  //     if (node) observer.current.observe(node);\n  //   },\n  //   [loading, hasMore, offset]\n  // );\n\n  useEffect(() => {\n    if (userData?.id && userData?.orgId) fetchTopCandidates();\n  }, [userData?.id, userData?.orgId]);\n\n  const debouncedHandleSearchInputChange = useCallback(\n    debounce(() => {\n      if (userData?.id && userData?.orgId) fetchMoreCandidatesApplications(0, true, searchStr);\n    }, 1500),\n    [userData?.id, userData?.orgId, searchStr, fetchMoreCandidatesApplications]\n  );\n\n  useEffect(() => {\n    if (userData?.id && userData?.orgId) debouncedHandleSearchInputChange();\n    return () => {\n      debouncedHandleSearchInputChange.cancel();\n    };\n  }, [userData?.id, userData?.orgId, debouncedHandleSearchInputChange]);\n\n  const handleArchiveCandidate = (candidate: CandidateApplication | topCandidateApplication) => {\n    setSelectedCandidate(candidate);\n    setShowArchiveModal(true);\n    setActiveDropdown(null);\n  };\n\n  const handleReviewCandidate = (candidate: CandidateApplication | topCandidateApplication) => {\n    setSelectedCandidate(candidate);\n    setShowReviewModal(true);\n    setActiveDropdown(null);\n  };\n\n  const onCancelReviewModal = () => {\n    setShowReviewModal(false);\n    setSelectedCandidate(null);\n  };\n\n  const onSubmitArchiveReason = async (reason: string) => {\n    if (!selectedCandidate) return;\n    try {\n      await archiveActiveApplication(selectedCandidate.applicationId, false, reason);\n      if (selectedCandidate.isTopApplication) {\n        setTopCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));\n      } else {\n        setCandidates((prev) => prev.filter((c) => c.applicationId !== selectedCandidate.applicationId));\n      }\n      setShowArchiveModal(false);\n      setSelectedCandidate(null);\n    } catch {}\n  };\n\n  const handleStatusChangeSuccess = (candidate: CandidateApplication | topCandidateApplication, status: string) => {\n    if (candidate?.isTopApplication) {\n      if (status === APPLICATION_STATUS.REJECTED) {\n        setTopCandidates((prev) => prev.filter((c) => c.applicationId !== candidate.applicationId));\n      } else {\n        setTopCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));\n      }\n    } else {\n      setCandidates((prev) => prev.map((c) => (c.applicationId === candidate.applicationId ? { ...c, applicationStatus: status } : c)));\n    }\n    setShowReviewModal(false);\n    setSelectedCandidate(null);\n  };\n\n  const handleCandidateClick = (candidateId: number) => {\n    router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${candidateId}`);\n  };\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        openDropdownId &&\n        dropdownRefs.current[openDropdownId] &&\n        !dropdownRefs.current[openDropdownId]?.contains(event.target as Node) &&\n        !(event.target as Element).closest(\".applications-sources-modal\")\n      ) {\n        setOpenDropdownId(null);\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, [openDropdownId]);\n\n  return (\n    <>\n      {loadingFullScreen && <FullPageLoader />}\n\n      <section className={`${style.resume_page} ${style.candidates_list_page}`}>\n        <div className=\"container\">\n          <div className=\"common-page-header\">\n            <div className=\"common-page-head-section\">\n              <div className=\"main-heading\">\n                <h2>\n                  <BackArrowIcon onClick={() => router.back()} />\n                  {t(\"candidates_for\")} <span>{searchParamsPromise.title}</span>\n                </h2>\n                <div className=\"right-action\">\n                  <Button\n                    className=\"primary-btn rounded-md button-sm\"\n                    onClick={() =>\n                      router.push(\n                        `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}?title=${searchParamsPromise.title}&jobUniqueId=${searchParamsPromise.jobUniqueId}`\n                      )\n                    }\n                  >\n                    {t(\"add_candidate\")}\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className={style.candidates_list_section}>\n            <div className={style.section_name}>\n              <h3> {t(\"top_ten_candidates\")} </h3>\n              <p> {t(\"based_on_interview_date\")} </p>\n            </div>\n            <div className=\"table-responsive\">\n              <table className=\"table  overflow-auto\">\n                <thead>\n                  <tr>\n                    <th style={{ width: \"20%\" }}> {t(\"candidate_name\")} </th>\n                    <th style={{ width: \"20%\" }}> {t(\"date_submitted\")} </th>\n                    <th style={{ width: \"20%\" }} className=\"text-center\">\n                      {t(\"ats_score\")}\n                    </th>\n                    {/* <th> {t(\"lined_up_for\")} </th> */}\n                    <th style={{ width: \"20%\" }} className=\"text-center\">\n                      {t(\"candidates_analysis\")}\n                    </th>\n\n                    <th style={{ width: \"20%\" }} className=\"text-center\">\n                      {\" \"}\n                      {t(\"actions\")}{\" \"}\n                    </th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {topCandidates.length > 0 ? (\n                    topCandidates.map((candidate, index) => {\n                      const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;\n                      const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;\n                      const dotClass = isPromoted ? \"green-dot\" : isDemoted ? \"red-dot\" : \"\";\n                      const statusClass =\n                        candidate.applicationStatus === APPLICATION_STATUS.APPROVED\n                          ? \"color-success\"\n                          : candidate.applicationStatus === APPLICATION_STATUS.REJECTED\n                            ? \"color-danger\"\n                            : \"color-dark\";\n                      return (\n                        <tr key={candidate.candidateId}>\n                          <td style={{ width: \"20%\" }}>\n                            <div\n                              onClick={() => handleCandidateClick(candidate.candidateId)}\n                              className={`color-primary cursor-pointer text-decoration-underline ${dotClass} d-inline`}\n                            >\n                              {index + 1}. {toTitleCase(candidate.candidateName)}\n                            </div>\n                          </td>\n                          <td style={{ width: \"20%\" }}>\n                            {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format(\"MMM D, YYYY\") : \"Not Available\"}\n                          </td>\n                          <td style={{ width: \"20%\" }} className=\"text-center\">\n                            {candidate.atsScore}\n                          </td>\n                          <td style={{ width: \"20%\", textAlign: \"center\" }} className={statusClass}>\n                            {candidate.applicationStatus}\n                          </td>\n\n                          {/* <td>{candidate.applicationRankStatus}</td> */}\n                          <td style={{ width: \"20%\" }} align=\"center\" className=\"position-relative\">\n                            <div onClick={() => (activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId))}>\n                              <Button className=\"clear-btn p-0\">\n                                <ThreeDotsIcon />\n                              </Button>\n                              {activeDropdown === candidate.candidateId && (\n                                <ul className=\"custom-dropdown\">\n                                  {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && (\n                                    <li\n                                      onClick={() =>\n                                        router.push(\n                                          `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`\n                                        )\n                                      }\n                                    >\n                                      {t(\"schedule_interview\")}{\" \"}\n                                    </li>\n                                  )}\n                                  <li\n                                    onClick={() => {\n                                      console.log(\"Add Candidate Info Clicked\", candidate.applicationId);\n                                      router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}?applicationId=${candidate.applicationId}`);\n                                    }}\n                                  >\n                                    {t(\"add_candidates_info\")}{\" \"}\n                                  </li>\n                                  {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (\n                                    <li onClick={() => handleReviewCandidate(candidate)}>{t(\"analyze_candidate_resume\")}</li>\n                                  )}\n                                  <li\n                                    onClick={\n                                      !disable\n                                        ? () =>\n                                            handlePromoteDemoteCandidate(\n                                              candidate as unknown as CandidateApplication,\n                                              APPLICATION_UPDATE_STATUS.DEMOTED\n                                            )\n                                        : undefined\n                                    }\n                                  >\n                                    {t(\"demote_candidate\")}\n                                  </li>\n                                  {hasArchiveRestoreCandidatesPermission && (\n                                    <li onClick={() => handleArchiveCandidate(candidate)}>{t(\"archive_candidate\")}</li>\n                                  )}\n                                </ul>\n                              )}\n                            </div>\n                          </td>\n                        </tr>\n                      );\n                    })\n                  ) : !loading ? (\n                    <tr>\n                      <td colSpan={5} className=\"text-center\">\n                        {t(topCandidates.length ? \"no_more_candidates_to_fetch\" : \"no_candidates_found\")}\n                      </td>\n                    </tr>\n                  ) : null}\n                </tbody>\n                {loading && <TableSkeleton rows={3} cols={5} colWidths=\"120,80,100,24,24\" />}\n              </table>\n            </div>\n          </div>\n\n          {/* Only show Other Candidates section if there are candidates */}\n          {candidates.length > 0 && (\n            <div className={style.candidates_list_section}>\n              <div className={`${style.section_name} d-flex align-items-center justify-content-between`}>\n                <div>\n                  <h3>{t(\"other_candidates\")}</h3>\n                  <p>{t(\"rancked_by_resume\")}</p>\n                </div>\n                {/* <div className=\"right-action w-25\">\n                  <InputWrapper className=\"mb-0 w-100 search-input\">\n                    <div className=\"icon-align right\">\n                      <Textbox\n                        className=\"form-control w-100\"\n                        control={control}\n                        name=\"search\"\n                        type=\"text\"\n                        placeholder=\"Search using name\"\n                        // onChange={(e) => setSearchStr(e.target.value)}\n                      >\n                        <InputWrapper.Icon>\n                          <SearchIcon />\n                        </InputWrapper.Icon>\n                      </Textbox>\n                    </div>\n                  </InputWrapper>\n                </div> */}\n              </div>\n              <div className=\"table-responsive\" id=\"scrollableCandidatesTableDiv\">\n                <InfiniteScroll\n                  dataLength={candidates.length}\n                  next={() => loadMoreCandidates()}\n                  hasMore={hasMore}\n                  height={window.innerHeight - 300}\n                  loader={\n                    loader && (\n                      <table className=\"table w-100\">\n                        <TableSkeleton rows={3} cols={5} colWidths=\"120,80,100,24,24\" />\n                      </table>\n                    )\n                  }\n                  endMessage={\n                    !loader && candidates.length ? (\n                      <table className=\"table w-100\">\n                        <tbody>\n                          <tr>\n                            <td colSpan={5} style={{ textAlign: \"center\", backgroundColor: \"#fff\" }}>\n                              {t(\"no_more_candidates_to_fetch\")}\n                            </td>\n                          </tr>\n                        </tbody>\n                      </table>\n                    ) : null\n                  }\n                >\n                  <table className=\"table overflow-auto mb-0\">\n                    <thead>\n                      <tr>\n                        <th style={{ width: \"20%\" }}>{t(\"candidate_name\")}</th>\n                        <th style={{ width: \"20%\" }}>{t(\"date_submitted\")}</th>\n                        {/* <th>{t(\"source\")}</th> */}\n                        <th style={{ width: \"20%\" }} className=\"text-center\">\n                          {t(\"ats_score\")}\n                        </th>\n\n                        <th style={{ width: \"20%\" }} className=\"text-center\">\n                          {t(\"candidates_analysis\")}\n                        </th>\n                        <th style={{ width: \"20%\" }} className=\"text-center\">\n                          {t(\"actions\")}\n                        </th>\n                      </tr>\n                    </thead>\n                    {candidates.length > 0 ? (\n                      <tbody id=\"scrollableCandidatesTableBody\">\n                        {candidates.map((candidate, index) => {\n                          const statusClass =\n                            candidate.applicationStatus === APPLICATION_STATUS.APPROVED\n                              ? \"color-success text-center\"\n                              : candidate.applicationStatus === APPLICATION_STATUS.REJECTED\n                                ? \"color-danger text-center\"\n                                : \"color-dark text-center\";\n\n                          const isPromoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.PROMOTED;\n                          const isDemoted = candidate.applicationRankStatus === APPLICATION_UPDATE_STATUS.DEMOTED;\n                          const dotClass = isPromoted ? \"green-dot\" : isDemoted ? \"red-dot\" : \"\";\n\n                          return (\n                            <tr\n                              key={candidate.candidateId}\n                              // ref={lastElementRef}\n                            >\n                              <td>\n                                <div\n                                  onClick={() => handleCandidateClick(candidate.candidateId)}\n                                  className={`color-primary cursor-pointer text-decoration-underline ${dotClass} d-inline`}\n                                >\n                                  {index + 1}. {toTitleCase(candidate.candidateName) || \"Candidate Name\"}\n                                </div>\n                              </td>\n                              <td>\n                                {candidate.applicationCreatedTs ? dayjs(candidate.applicationCreatedTs).format(\"MMM D, YYYY\") : \"Not Available\"}\n                              </td>\n                              <td className=\"text-center\">{candidate.atsScore ?? \"N/A\"}</td>\n                              <td className={statusClass}>{candidate.applicationStatus}</td>\n                              <td align=\"center\" className=\"position-relative\">\n                                <div onClick={() => (activeDropdown ? setActiveDropdown(null) : setActiveDropdown(candidate.candidateId))}>\n                                  <Button className=\"clear-btn p-0\">\n                                    <ThreeDotsIcon />\n                                  </Button>\n                                  {activeDropdown === candidate.candidateId && (\n                                    <ul\n                                      className=\"custom-dropdown\"\n                                      ref={(element) => {\n                                        if (element) {\n                                          dropdownRefs.current[String(candidate.candidateId)] = element;\n                                        }\n                                      }}\n                                    >\n                                      {candidate.applicationStatus === APPLICATION_STATUS.APPROVED && (\n                                        <li\n                                          onClick={() =>\n                                            router.push(\n                                              `${ROUTES.INTERVIEW.SCHEDULE_INTERVIEW}?applicationId=${candidate.applicationId}&candidateName=${encodeURIComponent(candidate.candidateName)}&title=${encodeURIComponent(searchParamsPromise.title)}&jobId=${paramsPromise.jobId}&jobUniqueId=${encodeURIComponent(searchParamsPromise.jobUniqueId)}`\n                                            )\n                                          }\n                                        >\n                                          {t(\"schedule_interview\")}\n                                        </li>\n                                      )}\n                                      <li onClick={() => router.push(`${ROUTES.INTERVIEW.ADD_CANDIDATE_INFO}/${candidate.applicationId}`)}>\n                                        {t(\"add_candidates_info\")}\n                                      </li>\n                                      {[APPLICATION_STATUS.PENDING, APPLICATION_STATUS.ON_HOLD].includes(candidate.applicationStatus) && (\n                                        <li onClick={() => handleReviewCandidate(candidate)}>{t(\"analyze_candidate_resume\")}</li>\n                                      )}\n                                      {/* <li>{t(\"analyze_candidate_resume\")}</li> */}\n                                      {candidate.applicationStatus !== APPLICATION_STATUS.REJECTED && (\n                                        <li\n                                          onClick={\n                                            !disable ? () => handlePromoteDemoteCandidate(candidate, APPLICATION_UPDATE_STATUS.PROMOTED) : undefined\n                                          }\n                                          style={disable ? { pointerEvents: \"none\", opacity: 0.5 } : {}}\n                                        >\n                                          {t(\"promote_candidate\")}\n                                        </li>\n                                      )}\n                                      {hasArchiveRestoreCandidatesPermission && (\n                                        <li onClick={() => handleArchiveCandidate(candidate)}>{t(\"archive_candidate\")}</li>\n                                      )}\n                                    </ul>\n                                  )}\n                                </div>\n                              </td>\n                            </tr>\n                          );\n                        })}\n                      </tbody>\n                    ) : (\n                      !loading && (\n                        <tbody>\n                          <tr>\n                            <td colSpan={5} style={{ textAlign: \"center\" }}>\n                              {t(\"no_candidates_found\")}\n                            </td>\n                          </tr>\n                        </tbody>\n                      )\n                    )}\n                  </table>\n                </InfiniteScroll>\n              </div>\n            </div>\n          )}\n        </div>\n      </section>\n\n      {showReviewModal && selectedCandidate && (\n        <CandidateApproveRejectModal onClickCancel={onCancelReviewModal} onSuccess={handleStatusChangeSuccess} candidate={selectedCandidate} />\n      )}\n\n      {showArchiveModal && selectedCandidate && (\n        <ArchiveCandidateModal\n          onClickCancel={() => setShowArchiveModal(false)}\n          applicationId={selectedCandidate.applicationId}\n          jobId={Number(paramsPromise.jobId)}\n          onSuccess={(reason) => onSubmitArchiveReason(reason)}\n        />\n      )}\n    </>\n  );\n};\n\nexport default CandidatesList;\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAG9C;AACA;AACA,6CAA6C;AAC7C;AACA;AACA,gEAAgE;AAEhE;AACA,qEAAqE;AACrE,2DAA2D;AAC3D,kEAAkE;AAClE;AACA;AACA;AACA,qRAAqF,EAAE;AAEvF,WAAW;AACX;AAKA;AAGA;AACA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAxCA;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,qEAAqE;AACrE,2DAA2D;AAC3D,kEAAkE;AAClE,6CAA6C;AAE7C,MAAM,iBAAiB,CAAC,EACtB,MAAM,EACN,YAAY,EAIb;IACC,iCAAiC;IACjC,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,QAAQ;IAChF,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAA+B,MAAM,IAAI,CAAC,WAAW,IAAI,EAAE;IAChG,MAAM,wCAAwC,gBAAgB,QAAQ,CAAC,mIAAA,CAAA,aAAU,CAAC,0BAA0B;IAC5G,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAChC,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAEtC,4BAA4B;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyD;IAClH,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,EAAE;IACnE,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,eAAe,qMAAA,CAAA,UAAK,CAAC,MAAM,CAA6C,CAAC;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO,cAAc,KAAK,KAAK,CAAC,oBAAoB,KAAK,EAAE;YAC9D,OAAO,IAAI,CAAC,0HAAA,CAAA,UAAM,CAAC,SAAS;QAC9B;IACF,GAAG;QAAC,cAAc,KAAK;KAAC;IAExB,8DAA8D;IAE9D,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU,SAAS,CAAC,eAAe,OAAO;QAE/C,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,iCAA8B,AAAD,EAAE,OAAO,cAAc,KAAK;YAChF,IAAI,UAAU,MAAM,SAAS;gBAC3B,iBAAiB,SAAS,IAAI,CAAC,IAAI;YACrC,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF,EAAE,OAAM;YACN,iBAAiB,EAAE;QACrB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,+BAA+B,OAAO,WAA2D;QACrG,WAAW;QACX,qBAAqB;QACrB,IAAI;YACF,MAAM,UAAU;gBACd,aAAa,UAAU,WAAW;gBAClC,eAAe,UAAU,aAAa;gBACtC;YACF;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,yBAAsB,AAAD,EAAE;YAE9C,IAAI,UAAU,MAAM,SAAS;gBAC3B,IAAI,WAAW,wIAAA,CAAA,4BAAyB,CAAC,QAAQ,EAAE;oBACjD,wDAAwD;oBACxD,MAAM,YAAY;oBAElB,+BAA+B;oBAC/B,cAAc,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,UAAU,aAAa;oBAEtF,wBAAwB;oBACxB,MAAM,WAAoC;wBACxC,eAAe,UAAU,aAAa;wBACtC,sBAAsB,UAAU,oBAAoB;wBACpD,UAAU,UAAU,QAAQ;wBAC5B,eAAe,UAAU,aAAa;wBACtC,uBAAuB,wIAAA,CAAA,4BAAyB,CAAC,QAAQ;wBACzD,aAAa,UAAU,WAAW;wBAClC,UAAU,UAAU,QAAQ;wBAC5B,YAAY,UAAU,UAAU;wBAChC,mBAAmB,UAAU,iBAAiB;wBAC9C,qBAAqB,UAAU,mBAAmB;wBAClD,sBAAsB,IAAI,OAAO,WAAW;wBAC5C,mBAAmB,UAAU,iBAAiB,IAAI;wBAClD,QAAQ,UAAU,MAAM,IAAI;wBAC5B,kBAAkB,UAAU,gBAAgB,IAAI;oBAClD;oBAEA,iBAAiB,CAAC,OAAS;+BAAI;4BAAM;yBAAS;gBAChD,OAAO,IAAI,WAAW,wIAAA,CAAA,4BAAyB,CAAC,OAAO,EAAE;oBACvD,sDAAsD;oBACtD,MAAM,UAAU;oBAEhB,6BAA6B;oBAC7B,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,QAAQ,aAAa;oBAEvF,0BAA0B;oBAC1B,MAAM,UAAgC;wBACpC,aAAa,QAAQ,WAAW;wBAChC,eAAe,QAAQ,aAAa;wBACpC,eAAe,QAAQ,aAAa;wBACpC,mBAAmB;wBACnB,mBAAmB;wBACnB,sBAAsB,QAAQ,oBAAoB;wBAClD,sBAAsB,IAAI,OAAO,WAAW;wBAC5C,UAAU;wBACV,QAAQ;wBACR,mBAAmB;wBACnB,qBAAqB;wBACrB,uBAAuB,wIAAA,CAAA,4BAAyB,CAAC,OAAO;wBACxD,UAAU,QAAQ,QAAQ;wBAC1B,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,UAAU;oBAChC;oBAEA,cAAc,CAAC,OAAS;+BAAI;4BAAM;yBAAQ;gBAC5C;YACF,OAAO;gBACL,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,UAAU,MAAM;YACtC;QACF,EAAE,OAAM,CACR,SAAU;YACR,kBAAkB;YAClB,WAAW;YACX,qBAAqB;QACvB;IACF;IAEA,MAAM,kCAAkC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,gBAAgB,CAAC,EAAE,QAAQ,KAAK,EAAE,YAAoB,EAAE;QACjH,IAAI,CAAC,UAAU,OAAO;QACtB,UAAU;QACV,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,8BAA2B,AAAD,EAAE;gBACjD,MAAM;gBACN,OAAO,mIAAA,CAAA,gBAAa;gBACpB,WAAW;gBACX,UAAU;gBACV,OAAO,OAAO,cAAc,KAAK;YACnC;YAEA,QAAQ,GAAG,CAAC,YAAY;YACxB,IAAI,UAAU,MAAM,SAAS;gBAC3B,MAAM,gBAAwC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;gBAErE,QAAQ,GAAG,CAAC,iBAAiB;gBAE7B,cAAc,CAAC,OAAU,QAAQ,gBAAgB;2BAAI;2BAAS;qBAAc;gBAC5E,IAAI,cAAc,MAAM,GAAG,mIAAA,CAAA,gBAAa,EAAE;oBACxC,WAAW;gBACb,OAAO;oBACL,WAAW;gBACb;gBACA,UAAU,gBAAgB,cAAc,MAAM;YAChD,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAM;YACN,WAAW;QACb,SAAU;YACR,UAAU;QACZ;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,UAAU,SAAS,gCAAgC,QAAQ,OAAO;IACzE;IAEA,sCAAsC;IACtC,oCAAoC;IACpC,2BAA2B;IAC3B,2DAA2D;IAC3D,iEAAiE;IACjE,wEAAwE;IACxE,UAAU;IACV,gDAAgD;IAChD,OAAO;IACP,+BAA+B;IAC/B,KAAK;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,MAAM,UAAU,OAAO;IACvC,GAAG;QAAC,UAAU;QAAI,UAAU;KAAM;IAElC,MAAM,mCAAmC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjD,CAAA,GAAA,kIAAA,CAAA,UAAQ,AAAD,EAAE;QACP,IAAI,UAAU,MAAM,UAAU,OAAO,gCAAgC,GAAG,MAAM;IAChF,GAAG,OACH;QAAC,UAAU;QAAI,UAAU;QAAO;QAAW;KAAgC;IAG7E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,MAAM,UAAU,OAAO;QACrC,OAAO;YACL,iCAAiC,MAAM;QACzC;IACF,GAAG;QAAC,UAAU;QAAI,UAAU;QAAO;KAAiC;IAEpE,MAAM,yBAAyB,CAAC;QAC9B,qBAAqB;QACrB,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,mBAAmB;QACxB,IAAI;YACF,MAAM,CAAA,GAAA,iLAAA,CAAA,2BAAwB,AAAD,EAAE,kBAAkB,aAAa,EAAE,OAAO;YACvE,IAAI,kBAAkB,gBAAgB,EAAE;gBACtC,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,kBAAkB,aAAa;YACnG,OAAO;gBACL,cAAc,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,kBAAkB,aAAa;YAChG;YACA,oBAAoB;YACpB,qBAAqB;QACvB,EAAE,OAAM,CAAC;IACX;IAEA,MAAM,4BAA4B,CAAC,WAA2D;QAC5F,IAAI,WAAW,kBAAkB;YAC/B,IAAI,WAAW,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;gBAC1C,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,aAAa,KAAK,UAAU,aAAa;YAC3F,OAAO;gBACL,iBAAiB,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,aAAa,KAAK,UAAU,aAAa,GAAG;4BAAE,GAAG,CAAC;4BAAE,mBAAmB;wBAAO,IAAI;YAClI;QACF,OAAO;YACL,cAAc,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,aAAa,KAAK,UAAU,aAAa,GAAG;wBAAE,GAAG,CAAC;wBAAE,mBAAmB;oBAAO,IAAI;QAC/H;QACA,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,aAAa;IAC/D;IACA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,kBACA,aAAa,OAAO,CAAC,eAAe,IACpC,CAAC,aAAa,OAAO,CAAC,eAAe,EAAE,SAAS,MAAM,MAAM,KAC5D,CAAC,AAAC,MAAM,MAAM,CAAa,OAAO,CAAC,gCACnC;gBACA,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;KAAe;IAEnB,qBACE;;YACG,mCAAqB,8OAAC,uJAAA,CAAA,UAAc;;;;;0BAErC,8OAAC;gBAAQ,WAAW,GAAG,yJAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,EAAE,yJAAA,CAAA,UAAK,CAAC,oBAAoB,EAAE;0BACtE,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,oJAAA,CAAA,UAAa;oDAAC,SAAS,IAAM,OAAO,IAAI;;;;;;gDACxC,EAAE;gDAAkB;8DAAC,8OAAC;8DAAM,oBAAoB,KAAK;;;;;;;;;;;;sDAExD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4IAAA,CAAA,UAAM;gDACL,WAAU;gDACV,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,EAAE,cAAc,KAAK,CAAC,OAAO,EAAE,oBAAoB,KAAK,CAAC,aAAa,EAAE,oBAAoB,WAAW,EAAE;0DAI7J,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOb,8OAAC;4BAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,uBAAuB;;8CAC3C,8OAAC;oCAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,YAAY;;sDAChC,8OAAC;;gDAAG;gDAAE,EAAE;gDAAsB;;;;;;;sDAC9B,8OAAC;;gDAAE;gDAAE,EAAE;gDAA2B;;;;;;;;;;;;;8CAEpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;0DACC,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;;gEAAG;gEAAE,EAAE;gEAAkB;;;;;;;sEACnD,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;;gEAAG;gEAAE,EAAE;gEAAkB;;;;;;;sEACnD,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;4DAAG,WAAU;sEACpC,EAAE;;;;;;sEAGL,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;4DAAG,WAAU;sEACpC,EAAE;;;;;;sEAGL,8OAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAM;4DAAG,WAAU;;gEACpC;gEACA,EAAE;gEAAY;;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;0DACE,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,WAAW;oDAC5B,MAAM,aAAa,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,QAAQ;oDACzF,MAAM,YAAY,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,OAAO;oDACvF,MAAM,WAAW,aAAa,cAAc,YAAY,YAAY;oDACpE,MAAM,cACJ,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACvD,kBACA,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACzD,iBACA;oDACR,qBACE,8OAAC;;0EACC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EACxB,cAAA,8OAAC;oEACC,SAAS,IAAM,qBAAqB,UAAU,WAAW;oEACzD,WAAW,CAAC,uDAAuD,EAAE,SAAS,SAAS,CAAC;;wEAEvF,QAAQ;wEAAE;wEAAG,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,aAAa;;;;;;;;;;;;0EAGrD,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EACvB,UAAU,oBAAoB,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,UAAU,oBAAoB,EAAE,MAAM,CAAC,iBAAiB;;;;;;0EAElG,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,UAAU,QAAQ;;;;;;0EAErB,8OAAC;gEAAG,OAAO;oEAAE,OAAO;oEAAO,WAAW;gEAAS;gEAAG,WAAW;0EAC1D,UAAU,iBAAiB;;;;;;0EAI9B,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,OAAM;gEAAS,WAAU;0EACpD,cAAA,8OAAC;oEAAI,SAAS,IAAO,iBAAiB,kBAAkB,QAAQ,kBAAkB,UAAU,WAAW;;sFACrG,8OAAC,4IAAA,CAAA,UAAM;4EAAC,WAAU;sFAChB,cAAA,8OAAC,oJAAA,CAAA,UAAa;;;;;;;;;;wEAEf,mBAAmB,UAAU,WAAW,kBACvC,8OAAC;4EAAG,WAAU;;gFACX,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,kBAC1D,8OAAC;oFACC,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,eAAe,EAAE,mBAAmB,UAAU,aAAa,EAAE,OAAO,EAAE,mBAAmB,oBAAoB,KAAK,EAAE,OAAO,EAAE,cAAc,KAAK,CAAC,aAAa,EAAE,mBAAmB,oBAAoB,WAAW,GAAG;;wFAIxS,EAAE;wFAAuB;;;;;;;8FAG9B,8OAAC;oFACC,SAAS;wFACP,QAAQ,GAAG,CAAC,8BAA8B,UAAU,aAAa;wFACjE,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,aAAa,EAAE;oFAC/F;;wFAEC,EAAE;wFAAwB;;;;;;;gFAE5B;oFAAC,0IAAA,CAAA,qBAAkB,CAAC,OAAO;oFAAE,0IAAA,CAAA,qBAAkB,CAAC,OAAO;iFAAC,CAAC,QAAQ,CAAC,UAAU,iBAAiB,mBAC5F,8OAAC;oFAAG,SAAS,IAAM,sBAAsB;8FAAa,EAAE;;;;;;8FAE1D,8OAAC;oFACC,SACE,CAAC,UACG,IACE,6BACE,WACA,wIAAA,CAAA,4BAAyB,CAAC,OAAO,IAErC;8FAGL,EAAE;;;;;;gFAEJ,uDACC,8OAAC;oFAAG,SAAS,IAAM,uBAAuB;8FAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;uDA/D5D,UAAU,WAAW;;;;;gDAuElC,KACE,CAAC,wBACH,8OAAC;8DACC,cAAA,8OAAC;wDAAG,SAAS;wDAAG,WAAU;kEACvB,EAAE,cAAc,MAAM,GAAG,gCAAgC;;;;;;;;;;2DAG5D;;;;;;4CAEL,yBAAW,8OAAC,yJAAA,CAAA,UAAa;gDAAC,MAAM;gDAAG,MAAM;gDAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAM5D,WAAW,MAAM,GAAG,mBACnB,8OAAC;4BAAI,WAAW,yJAAA,CAAA,UAAK,CAAC,uBAAuB;;8CAC3C,8OAAC;oCAAI,WAAW,GAAG,yJAAA,CAAA,UAAK,CAAC,YAAY,CAAC,kDAAkD,CAAC;8CACvF,cAAA,8OAAC;;0DACC,8OAAC;0DAAI,EAAE;;;;;;0DACP,8OAAC;0DAAG,EAAE;;;;;;;;;;;;;;;;;8CAqBV,8OAAC;oCAAI,WAAU;oCAAmB,IAAG;8CACnC,cAAA,8OAAC,+KAAA,CAAA,UAAc;wCACb,YAAY,WAAW,MAAM;wCAC7B,MAAM,IAAM;wCACZ,SAAS;wCACT,QAAQ,OAAO,WAAW,GAAG;wCAC7B,QACE,wBACE,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC,yJAAA,CAAA,UAAa;gDAAC,MAAM;gDAAG,MAAM;gDAAG,WAAU;;;;;;;;;;;wCAIjD,YACE,CAAC,UAAU,WAAW,MAAM,iBAC1B,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;0DACC,cAAA,8OAAC;8DACC,cAAA,8OAAC;wDAAG,SAAS;wDAAG,OAAO;4DAAE,WAAW;4DAAU,iBAAiB;wDAAO;kEACnE,EAAE;;;;;;;;;;;;;;;;;;;;qDAKT;kDAGN,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;8DACC,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EAAI,EAAE;;;;;;0EAChC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;0EAAI,EAAE;;;;;;0EAEhC,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,EAAE;;;;;;0EAGL,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,EAAE;;;;;;0EAEL,8OAAC;gEAAG,OAAO;oEAAE,OAAO;gEAAM;gEAAG,WAAU;0EACpC,EAAE;;;;;;;;;;;;;;;;;gDAIR,WAAW,MAAM,GAAG,kBACnB,8OAAC;oDAAM,IAAG;8DACP,WAAW,GAAG,CAAC,CAAC,WAAW;wDAC1B,MAAM,cACJ,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACvD,8BACA,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,GACzD,6BACA;wDAER,MAAM,aAAa,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,QAAQ;wDACzF,MAAM,YAAY,UAAU,qBAAqB,KAAK,wIAAA,CAAA,4BAAyB,CAAC,OAAO;wDACvF,MAAM,WAAW,aAAa,cAAc,YAAY,YAAY;wDAEpE,qBACE,8OAAC;;8EAIC,8OAAC;8EACC,cAAA,8OAAC;wEACC,SAAS,IAAM,qBAAqB,UAAU,WAAW;wEACzD,WAAW,CAAC,uDAAuD,EAAE,SAAS,SAAS,CAAC;;4EAEvF,QAAQ;4EAAE;4EAAG,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,aAAa,KAAK;;;;;;;;;;;;8EAG1D,8OAAC;8EACE,UAAU,oBAAoB,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,UAAU,oBAAoB,EAAE,MAAM,CAAC,iBAAiB;;;;;;8EAElG,8OAAC;oEAAG,WAAU;8EAAe,UAAU,QAAQ,IAAI;;;;;;8EACnD,8OAAC;oEAAG,WAAW;8EAAc,UAAU,iBAAiB;;;;;;8EACxD,8OAAC;oEAAG,OAAM;oEAAS,WAAU;8EAC3B,cAAA,8OAAC;wEAAI,SAAS,IAAO,iBAAiB,kBAAkB,QAAQ,kBAAkB,UAAU,WAAW;;0FACrG,8OAAC,4IAAA,CAAA,UAAM;gFAAC,WAAU;0FAChB,cAAA,8OAAC,oJAAA,CAAA,UAAa;;;;;;;;;;4EAEf,mBAAmB,UAAU,WAAW,kBACvC,8OAAC;gFACC,WAAU;gFACV,KAAK,CAAC;oFACJ,IAAI,SAAS;wFACX,aAAa,OAAO,CAAC,OAAO,UAAU,WAAW,EAAE,GAAG;oFACxD;gFACF;;oFAEC,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,kBAC1D,8OAAC;wFACC,SAAS,IACP,OAAO,IAAI,CACT,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,aAAa,CAAC,eAAe,EAAE,mBAAmB,UAAU,aAAa,EAAE,OAAO,EAAE,mBAAmB,oBAAoB,KAAK,EAAE,OAAO,EAAE,cAAc,KAAK,CAAC,aAAa,EAAE,mBAAmB,oBAAoB,WAAW,GAAG;kGAIxS,EAAE;;;;;;kGAGP,8OAAC;wFAAG,SAAS,IAAM,OAAO,IAAI,CAAC,GAAG,0HAAA,CAAA,UAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,EAAE,UAAU,aAAa,EAAE;kGAC/F,EAAE;;;;;;oFAEJ;wFAAC,0IAAA,CAAA,qBAAkB,CAAC,OAAO;wFAAE,0IAAA,CAAA,qBAAkB,CAAC,OAAO;qFAAC,CAAC,QAAQ,CAAC,UAAU,iBAAiB,mBAC5F,8OAAC;wFAAG,SAAS,IAAM,sBAAsB;kGAAa,EAAE;;;;;;oFAGzD,UAAU,iBAAiB,KAAK,0IAAA,CAAA,qBAAkB,CAAC,QAAQ,kBAC1D,8OAAC;wFACC,SACE,CAAC,UAAU,IAAM,6BAA6B,WAAW,wIAAA,CAAA,4BAAyB,CAAC,QAAQ,IAAI;wFAEjG,OAAO,UAAU;4FAAE,eAAe;4FAAQ,SAAS;wFAAI,IAAI,CAAC;kGAE3D,EAAE;;;;;;oFAGN,uDACC,8OAAC;wFAAG,SAAS,IAAM,uBAAuB;kGAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;2DA3D9D,UAAU,WAAW;;;;;oDAmEhC;;;;;2DAGF,CAAC,yBACC,8OAAC;8DACC,cAAA,8OAAC;kEACC,cAAA,8OAAC;4DAAG,SAAS;4DAAG,OAAO;gEAAE,WAAW;4DAAS;sEAC1C,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAc1B,mBAAmB,mCAClB,8OAAC,iKAAA,CAAA,UAA2B;gBAAC,eAAe;gBAAqB,WAAW;gBAA2B,WAAW;;;;;;YAGnH,oBAAoB,mCACnB,8OAAC,2JAAA,CAAA,UAAqB;gBACpB,eAAe,IAAM,oBAAoB;gBACzC,eAAe,kBAAkB,aAAa;gBAC9C,OAAO,OAAO,cAAc,KAAK;gBACjC,WAAW,CAAC,SAAW,sBAAsB;;;;;;;;AAKvD;uCAEe", "debugId": null}}, {"offset": {"line": 2599, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2605, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/stratum%209/stratum9-hiring-web/src/app/candidates-list/%5BjobId%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport CandidatesList from \"@/components/views/resume/CandidatesList\";\nimport React from \"react\";\n\nconst page = ({ params, searchParams }: { params: Promise<{ jobId: string }>; searchParams: Promise<{ title: string; jobUniqueId: string }> }) => {\n  return (\n    <div>\n      <CandidatesList params={params} searchParams={searchParams} />\n    </div>\n  );\n};\n\nexport default page;\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAIA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAyG;IAC3I,qBACE,8OAAC;kBACC,cAAA,8OAAC,uJAAA,CAAA,UAAc;YAAC,QAAQ;YAAQ,cAAc;;;;;;;;;;;AAGpD;uCAEe", "debugId": null}}, {"offset": {"line": 2630, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}